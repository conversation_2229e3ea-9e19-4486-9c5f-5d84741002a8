version: '3.8'

services:
  # Qdrant 向量数据库
  qdrant:
    image: qdrant/qdrant:latest
    container_name: qdrant-shadow
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    restart: unless-stopped
    networks:
      - shadow-network

  # Caby Vision 服务（包含影子模式）
  caby-vision:
    build:
      context: ./caby_vision
      dockerfile: Dockerfile.infrared
    container_name: caby-vision-shadow
    ports:
      - "8001:8001"
    volumes:
      - ./caby_vision/models:/app/models
      - ./caby_vision/config:/app/config
      - ./caby_vision/logs:/app/logs
    environment:
      - INFRARED_DEVICE=cpu  # 或 cuda（如果有GPU）
      - INFRARED_MODEL_PATH=/app/models/infrared/infrared_cat_model_quantized.onnx
      - INFRARED_FEATURES_PATH=/app/models/infrared/reference_features.json
    depends_on:
      - qdrant
    restart: unless-stopped
    networks:
      - shadow-network

  # Caby AI 服务
  caby-ai:
    build:
      context: ./caby_ai
      dockerfile: Dockerfile
    container_name: caby-ai-shadow
    ports:
      - "8765:8765"
    volumes:
      - ./caby_ai/config:/app/config
      - ./caby_ai/logs:/app/logs
    environment:
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
      - VISION_HOST=caby-vision
      - VISION_PORT=8001
      - RECOGNITION_SIMILARITY_THRESHOLD=0.8
      - RECOGNITION_DEFAULT_MODEL_TYPE=infrared
    depends_on:
      - qdrant
      - caby-vision
    restart: unless-stopped
    networks:
      - shadow-network

  # Backend Server
  backend-server:
    build:
      context: ./backend_server
      dockerfile: Dockerfile
    container_name: backend-server-shadow
    ports:
      - "8080:8080"
    volumes:
      - ./backend_server/config:/app/config
      - ./backend_server/logs:/app/logs
    environment:
      - CABY_AI_BASE_URL=http://caby-ai:8765
      - CABY_AI_API_KEY=caby-token
      - DATABASE_URL=****************************************/cabycare
    depends_on:
      - caby-ai
      - postgres
    restart: unless-stopped
    networks:
      - shadow-network

  # PostgreSQL 数据库
  postgres:
    image: postgres:13
    container_name: postgres-shadow
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=cabycare
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    restart: unless-stopped
    networks:
      - shadow-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: redis-shadow
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - shadow-network

volumes:
  qdrant_data:
  postgres_data:
  redis_data:

networks:
  shadow-network:
    driver: bridge
