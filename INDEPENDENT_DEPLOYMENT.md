# 猫咪个体识别影子模式独立部署指南

## 概述

本指南描述如何在不依赖 `reid` 目录的情况下，独立部署猫咪个体识别影子模式系统。各个服务可以部署在不同的服务器上。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Backend Server │    │    Caby AI      │    │   Caby Vision   │
│   (Port 8080)   │◄──►│   (Port 8765)   │◄──►│   (Port 8001)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Qdrant      │    │   Model Files   │
│   (Port 5432)   │    │   (Port 6333)   │    │   (Local Dir)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 前置要求

### 硬件要求
- **Caby Vision**: 2GB+ RAM, 可选 GPU
- **Caby AI**: 1GB+ RAM
- **Backend Server**: 1GB+ RAM
- **Qdrant**: 512MB+ RAM
- **PostgreSQL**: 512MB+ RAM

### 软件要求
- Docker & Docker Compose
- Python 3.9+
- Go 1.19+

## 部署步骤

### 1. 准备模型文件

#### 方法一：从现有 reid 目录复制
```bash
# 在有 reid 目录的机器上执行
cd caby_vision
python scripts/setup_infrared_model.py
```

#### 方法二：手动下载/复制
```bash
# 创建目录结构
mkdir -p caby_vision/models/infrared

# 复制以下文件到 caby_vision/models/infrared/：
# - infrared_cat_model_quantized.onnx
# - reference_features.json
# - infrared_cat_recognizer.py
```

#### 方法三：从云存储下载
```bash
# 示例：从 S3 下载
aws s3 cp s3://your-bucket/models/ caby_vision/models/infrared/ --recursive

# 示例：从 HTTP 下载
wget -P caby_vision/models/infrared/ \
  https://your-server.com/models/infrared_cat_model_quantized.onnx
```

### 2. 配置文件设置

#### Caby Vision 配置
```bash
# 编辑 caby_vision/config/infrared_config.yaml
cat > caby_vision/config/infrared_config.yaml << EOF
infrared_model:
  model_path: "models/infrared/infrared_cat_model_quantized.onnx"
  reference_features_path: "models/infrared/reference_features.json"
  device: "auto"  # 或 "cpu", "cuda"
  input_size: 224
  feature_dim: 512
  k_neighbors: 7
  distance_metric: "cosine"
EOF
```

#### Caby AI 配置
```bash
# 编辑 caby_ai/config/config.yaml
# 确保包含以下配置：
recognition:
  similarity_threshold: 0.8
  default_model_type: "infrared"

vision:
  host: "caby-vision"  # 或实际的 IP 地址
  port: 8001
  api_key: "default_api_key"

qdrant:
  host: "qdrant"  # 或实际的 IP 地址
  port: 6333
  scheme: "http"
```

#### Backend Server 配置
```bash
# 编辑 backend_server/config/config.yaml
# 确保包含以下配置：
caby_ai:
  base_url: "http://caby-ai:8765"  # 或实际的 URL
  api_key: "caby-token"
  max_retries: 300
  timeout_minutes: 30
```

### 3. 部署选项

#### 选项一：Docker Compose 一键部署
```bash
# 使用提供的 docker-compose 文件
docker-compose -f docker-compose.shadow-mode.yml up -d

# 查看服务状态
docker-compose -f docker-compose.shadow-mode.yml ps

# 查看日志
docker-compose -f docker-compose.shadow-mode.yml logs -f
```

#### 选项二：分别部署到不同服务器

##### 服务器 1: Qdrant + PostgreSQL
```bash
# 启动 Qdrant
docker run -d --name qdrant \
  -p 6333:6333 -p 6334:6334 \
  -v qdrant_data:/qdrant/storage \
  qdrant/qdrant:latest

# 启动 PostgreSQL
docker run -d --name postgres \
  -p 5432:5432 \
  -e POSTGRES_DB=cabycare \
  -e POSTGRES_USER=user \
  -e POSTGRES_PASSWORD=password \
  -v postgres_data:/var/lib/postgresql/data \
  postgres:13
```

##### 服务器 2: Caby Vision
```bash
cd caby_vision

# 构建镜像
docker build -f Dockerfile.infrared -t caby-vision:shadow .

# 启动服务
docker run -d --name caby-vision \
  -p 8001:8001 \
  -v $(pwd)/models:/app/models \
  -v $(pwd)/config:/app/config \
  -e INFRARED_DEVICE=cpu \
  caby-vision:shadow
```

##### 服务器 3: Caby AI
```bash
cd caby_ai

# 更新配置文件中的服务地址
# vision.host: "服务器2的IP"
# qdrant.host: "服务器1的IP"

# 构建并启动
docker build -t caby-ai:shadow .
docker run -d --name caby-ai \
  -p 8765:8765 \
  -v $(pwd)/config:/app/config \
  caby-ai:shadow
```

##### 服务器 4: Backend Server
```bash
cd backend_server

# 更新配置文件中的服务地址
# caby_ai.base_url: "http://服务器3的IP:8765"

# 构建并启动
docker build -t backend-server:shadow .
docker run -d --name backend-server \
  -p 8080:8080 \
  -v $(pwd)/config:/app/config \
  backend-server:shadow
```

### 4. 验证部署

#### 检查服务健康状态
```bash
# Caby Vision
curl http://localhost:8001/health

# Caby AI
curl http://localhost:8765/api/v1/health

# Backend Server
curl http://localhost:8080/health
```

#### 测试影子模式功能
```bash
# 测试红外识别端点
curl -X POST http://localhost:8001/predict_infrared \
  -H "Authorization: Bearer default_api_key" \
  -H "Content-Type: application/json" \
  -d '{"image": "base64_image_data", "return_features": true}'

# 测试个体识别
curl -X POST http://localhost:8765/api/v1/recognition/identify \
  -H "Authorization: Bearer caby-token" \
  -F "image=@test_cat.jpg" \
  -F "user_id=test_user" \
  -F "model_type=infrared"
```

### 5. 环境变量配置

#### Caby Vision 环境变量
```bash
export INFRARED_MODEL_PATH="/app/models/infrared/infrared_cat_model_quantized.onnx"
export INFRARED_FEATURES_PATH="/app/models/infrared/reference_features.json"
export INFRARED_DEVICE="cpu"  # 或 "cuda"
```

#### Caby AI 环境变量
```bash
export QDRANT_HOST="qdrant-server-ip"
export QDRANT_PORT="6333"
export VISION_HOST="caby-vision-server-ip"
export VISION_PORT="8001"
export RECOGNITION_SIMILARITY_THRESHOLD="0.8"
```

#### Backend Server 环境变量
```bash
export CABY_AI_BASE_URL="http://caby-ai-server-ip:8765"
export CABY_AI_API_KEY="caby-token"
export DATABASE_URL="**************************************************/cabycare"
```

### 6. 监控和日志

#### 日志收集
```bash
# 查看容器日志
docker logs -f caby-vision
docker logs -f caby-ai
docker logs -f backend-server

# 使用 docker-compose
docker-compose -f docker-compose.shadow-mode.yml logs -f [service_name]
```

#### 性能监控
```bash
# 监控资源使用
docker stats

# 监控 Qdrant
curl http://qdrant-server:6333/metrics

# 监控应用健康状态
watch -n 5 'curl -s http://localhost:8001/health | jq'
```

### 7. 故障排除

#### 常见问题

1. **模型文件未找到**
   ```bash
   # 检查文件是否存在
   ls -la caby_vision/models/infrared/
   
   # 检查权限
   chmod 644 caby_vision/models/infrared/*
   ```

2. **服务间连接失败**
   ```bash
   # 检查网络连通性
   docker exec caby-ai ping caby-vision
   
   # 检查端口是否开放
   telnet caby-vision-ip 8001
   ```

3. **Qdrant 连接失败**
   ```bash
   # 检查 Qdrant 状态
   curl http://qdrant-server:6333/health
   
   # 检查集合是否创建
   curl http://qdrant-server:6333/collections
   ```

### 8. 扩展和优化

#### 负载均衡
```bash
# 使用 nginx 进行负载均衡
# 配置多个 caby-vision 实例
docker run -d --name caby-vision-1 -p 8001:8001 caby-vision:shadow
docker run -d --name caby-vision-2 -p 8002:8001 caby-vision:shadow
```

#### GPU 支持
```bash
# 启用 GPU 支持
docker run --gpus all -d --name caby-vision \
  -e INFRARED_DEVICE=cuda \
  caby-vision:shadow
```

#### 数据备份
```bash
# 备份 Qdrant 数据
docker exec qdrant tar -czf /tmp/qdrant-backup.tar.gz /qdrant/storage
docker cp qdrant:/tmp/qdrant-backup.tar.gz ./

# 备份 PostgreSQL 数据
docker exec postgres pg_dump -U user cabycare > backup.sql
```

## 总结

通过以上步骤，您可以在完全独立的环境中部署猫咪个体识别影子模式系统，无需依赖 `reid` 目录。系统支持：

- ✅ 独立的服务部署
- ✅ 跨服务器部署
- ✅ Docker 容器化
- ✅ 环境变量配置
- ✅ 健康检查和监控
- ✅ 故障排除指南

每个服务都可以独立扩展和维护，为生产环境提供了灵活的部署选项。
