# 红外猫咪识别模型配置
infrared_model:
  # 模型文件路径（相对于 caby_vision 根目录）
  model_path: "models/infrared/infrared_cat_model_quantized.onnx"
  
  # 参考特征文件路径
  reference_features_path: "models/infrared/reference_features.json"
  
  # 设备配置
  device: "auto"  # auto, cpu, cuda
  
  # 模型参数
  input_size: 224
  feature_dim: 512
  
  # KNN 参数
  k_neighbors: 7
  distance_metric: "cosine"
  
  # 性能配置
  batch_size: 1
  num_workers: 1
  
# 环境变量覆盖
# 可以通过以下环境变量覆盖配置：
# INFRARED_MODEL_PATH - 模型文件路径
# INFRARED_FEATURES_PATH - 参考特征文件路径
# INFRARED_DEVICE - 运行设备
