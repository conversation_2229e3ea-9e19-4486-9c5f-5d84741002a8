#!/usr/bin/env python3
"""
测试独立部署的红外猫咪识别系统
"""

import os
import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_file_structure():
    """测试文件结构是否正确"""
    logger.info("Testing file structure...")
    
    base_dir = Path(__file__).parent
    required_files = [
        base_dir / "models" / "infrared" / "infrared_cat_model_quantized.onnx",
        base_dir / "models" / "infrared" / "reference_features.json", 
        base_dir / "models" / "infrared" / "infrared_cat_recognizer.py",
        base_dir / "config" / "infrared_config.yaml"
    ]
    
    all_files_exist = True
    for file_path in required_files:
        if file_path.exists():
            logger.info(f"✓ {file_path.relative_to(base_dir)}")
        else:
            logger.error(f"✗ {file_path.relative_to(base_dir)} not found")
            all_files_exist = False
    
    return all_files_exist

def test_config_loading():
    """测试配置加载"""
    logger.info("Testing config loading...")
    
    try:
        # 添加 triton_service 到路径
        triton_service_path = Path(__file__).parent / "triton_service"
        sys.path.insert(0, str(triton_service_path))
        
        from infrared_config import get_infrared_config
        
        config = get_infrared_config()
        summary = config.get_config_summary()
        
        logger.info("Config loaded successfully:")
        for key, value in summary.items():
            logger.info(f"  {key}: {value}")
        
        return summary.get('paths_valid', False)
        
    except Exception as e:
        logger.error(f"Config loading failed: {e}")
        return False

def test_infrared_detector():
    """测试红外检测器初始化"""
    logger.info("Testing infrared detector initialization...")
    
    try:
        # 添加 triton_service 到路径
        triton_service_path = Path(__file__).parent / "triton_service"
        sys.path.insert(0, str(triton_service_path))
        
        from infrared_cat_detector import InfraredCatDetector
        
        detector = InfraredCatDetector()
        health = detector.health_check()
        
        logger.info("Detector health check:")
        for key, value in health.items():
            logger.info(f"  {key}: {value}")
        
        return health.get("model_loaded", False)
        
    except Exception as e:
        logger.error(f"Detector initialization failed: {e}")
        return False

def test_independence():
    """测试是否真正独立于 reid 目录"""
    logger.info("Testing independence from reid directory...")
    
    # 检查是否有任何对 reid 目录的引用
    base_dir = Path(__file__).parent
    
    # 检查 Python 路径中是否有 reid 相关路径
    reid_paths = [path for path in sys.path if 'reid' in path.lower()]
    if reid_paths:
        logger.warning(f"Found reid paths in sys.path: {reid_paths}")
    
    # 检查当前工作目录
    cwd = Path.cwd()
    if 'reid' in str(cwd).lower():
        logger.warning(f"Current working directory contains 'reid': {cwd}")
    
    # 检查环境变量
    reid_env_vars = {k: v for k, v in os.environ.items() if 'reid' in k.lower() or 'reid' in v.lower()}
    if reid_env_vars:
        logger.warning(f"Found reid-related environment variables: {reid_env_vars}")
    
    logger.info("Independence test completed")
    return True

def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("Testing Independent Deployment of Infrared Cat Recognition")
    logger.info("=" * 60)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Config Loading", test_config_loading),
        ("Infrared Detector", test_infrared_detector),
        ("Independence", test_independence)
    ]
    
    results = {}
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} Test ---")
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{test_name}: {status}")
        except Exception as e:
            logger.error(f"{test_name} test error: {e}")
            results[test_name] = False
    
    # 总结
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Independent deployment is ready.")
        return True
    else:
        logger.error("❌ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
