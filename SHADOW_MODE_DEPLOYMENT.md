# 猫咪个体识别影子模式部署文档

## 概述

本文档描述了将 `reid/infrared_cat_recognition_project` 中的红外猫咪识别模型部署到 `caby_vision` 中作为影子模式运行的完整实现。

## 系统架构

```
Backend Server ──────────────────────────────────────────────────────────┐
    │                                                                    │
    │ 1. 接收图片识别请求                                                    │
    │ 2. 调用 caby_ai 进行个体识别                                          │
    │ 3. 处理新猫咪注册和同步                                               │
    │ 4. 发送影子模式通知                                                   │
    │                                                                    │
    ▼                                                                    │
Caby AI ─────────────────────────────────────────────────────────────────┤
    │                                                                    │
    │ 1. 同时调用两个模型进行识别                                            │
    │ 2. 在 Qdrant 中搜索相似特征                                          │
    │ 3. 处理用户权限约束                                                   │
    │ 4. 返回对比结果                                                      │
    │                                                                    │
    ▼                                                                    │
Caby Vision ─────────────────────────────────────────────────────────────┘
    │
    │ 1. 主模型：原始猫咪识别
    │ 2. 影子模型：红外猫咪识别
    │ 3. 同时返回两个模型的结果
    │ 4. 支持 CPU/GPU 运行模式
```

## 核心功能

### 1. 影子模式运行
- ✅ 每次识别请求同时调用原始模型和红外模型
- ✅ 红外模型作为主要决策依据
- ✅ 原始模型作为对比参考
- ✅ 支持 CPU 和 GPU 运行模式

### 2. 个体识别链路
- ✅ Backend Server → Caby AI → Caby Vision
- ✅ 特征向量提取和相似度对比
- ✅ 用户权限约束的特征库访问
- ✅ 可配置的相似度阈值

### 3. 新猫咪处理
- ✅ 自动生成 `NewCat<DateTime>` 格式的名称
- ✅ Backend 和 Caby AI 的 cat_id 同步
- ✅ 同时注册到两个模型的特征库

### 4. 影子模式通知
- ✅ 消息推送显示对比结果
- ✅ 标题后添加括号格式：`小白（小白）` 或 `小白（小白 vs 小花）`
- ✅ 统计一致性和差异性

## 文件结构

### Caby Vision 修改
```
caby_vision/triton_service/
├── infrared_cat_detector.py          # 红外猫咪检测器适配器
├── caby_vision_detector.py           # 修改：添加影子模式支持
├── caby_vision_server.py             # 修改：新增 API 端点
└── triton_server.py                  # 修改：新增路由
```

### Caby AI 新增
```
caby_ai/pkg/
├── qdrant/
│   └── cat_features.go               # 猫咪特征数据库客户端
├── recognition/
│   └── cat_recognition.go            # 个体识别服务
└── api/
    └── recognition_handler.go        # 识别 API 处理器
```

### Backend Server 新增
```
backend_server/pkg/cattoilet/
├── cat_recognition.go                # Caby AI 客户端
└── shadow_notification.go           # 影子模式通知服务
```

## API 端点

### Caby Vision 新增端点
- `POST /predict_infrared` - 红外猫咪识别
- `POST /features` - 提取特征向量
- `POST /features_infrared` - 提取红外特征向量

### Caby AI 新增端点
- `POST /api/v1/recognition/identify` - 个体识别
- `POST /api/v1/recognition/register` - 注册新猫咪
- `GET /api/v1/recognition/cats` - 获取用户猫咪列表
- `DELETE /api/v1/recognition/cats` - 删除猫咪

### Backend Server 新增端点
- `POST /api/cats/recognize` - 猫咪个体识别

## 配置文件

### Caby AI 配置 (`caby_ai/config/config.yaml`)
```yaml
recognition:
  similarity_threshold: 0.8
  default_model_type: "infrared"
```

### Backend Server 配置 (`backend_server/config/config.yaml`)
```yaml
caby_ai:
  base_url: "http://localhost:8765"
  api_key: "caby-token"
  max_retries: 300
  timeout_minutes: 30
```

## 部署步骤

### 1. 准备模型文件
确保以下文件存在：
- `reid/infrared_cat_recognition_project/deployment/infrared_cat_model_quantized.onnx`
- `reid/infrared_cat_recognition_project/deployment/reference_features.json`

### 2. 启动 Qdrant
```bash
docker run -p 6333:6333 qdrant/qdrant
```

### 3. 启动 Caby Vision
```bash
cd caby_vision
python triton_service/caby_vision_server.py
```

### 4. 启动 Caby AI
```bash
cd caby_ai
go run main.go
```

### 5. 启动 Backend Server
```bash
cd backend_server
go run main.go
```

## 测试验证

### 使用测试脚本
```bash
# 测试 Caby AI
python caby_ai/scripts/test_recognition.py recognize cat.jpg user123 infrared

# 测试完整链路
python backend_server/scripts/test_cat_recognition.py cat.jpg user123 your_token
```

### 手动测试
```bash
# 测试 Caby Vision 影子模式
curl -X POST http://localhost:8001/predict \
  -H "Authorization: Bearer default_api_key" \
  -H "Content-Type: application/json" \
  -d '{"image": "base64_image_data", "return_features": true}'

# 测试 Caby AI 个体识别
curl -X POST http://localhost:8765/api/v1/recognition/identify \
  -H "Authorization: Bearer caby-token" \
  -F "image=@cat.jpg" \
  -F "user_id=user123" \
  -F "model_type=infrared"
```

## 影子模式特性

### 1. 双模型对比
- 每次请求同时运行两个模型
- 记录一致性和差异性
- 生成对比报告

### 2. 通知系统
- 实时推送识别结果对比
- 格式化消息显示
- 统计分析功能

### 3. 用户权限
- 基于用户ID的特征库隔离
- 只能访问自己的猫咪特征
- 安全的数据访问控制

## 监控和维护

### 日志监控
- Caby Vision: 影子模式调用日志
- Caby AI: 识别结果对比日志
- Backend Server: 通知发送日志

### 性能指标
- 模型一致性率
- 识别准确率
- 响应时间
- 错误率

### 故障处理
- 影子模式失败不影响主流程
- 自动降级到单模型运行
- 详细的错误日志记录

## 未来扩展

### 1. 模型管理
- 动态模型切换
- A/B 测试支持
- 模型版本管理

### 2. 通知增强
- 邮件通知
- 移动推送
- 实时 WebSocket

### 3. 分析报告
- 定期生成对比报告
- 模型性能分析
- 用户行为分析

## 注意事项

1. **资源消耗**: 影子模式会增加计算资源消耗
2. **延迟影响**: 同时调用两个模型可能增加响应时间
3. **存储需求**: 需要为两个模型分别存储特征向量
4. **监控重要**: 需要密切监控两个模型的性能差异

## 总结

影子模式部署成功实现了以下目标：
- ✅ 红外猫咪识别模型集成到 caby_vision
- ✅ 完整的个体识别链路建立
- ✅ 用户权限约束的特征库访问
- ✅ 影子模式结果推送和通知
- ✅ 新猫咪注册和同步机制
- ✅ CPU/GPU 运行模式支持

系统现在可以同时运行两个模型进行对比，为未来完全替换提供了平滑的过渡方案。
