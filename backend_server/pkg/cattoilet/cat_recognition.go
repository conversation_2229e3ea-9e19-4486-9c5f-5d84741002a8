package cattoilet

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"time"

	"cabycare-server/config"
)

// CatRecognitionClient caby_ai个体识别客户端
type CatRecognitionClient struct {
	cfg        *config.Config
	httpClient *http.Client
	baseURL    string
	apiKey     string
}

// NewCatRecognitionClient 创建新的个体识别客户端
func NewCatRecognitionClient(cfg *config.Config) *CatRecognitionClient {
	return &CatRecognitionClient{
		cfg:        cfg,
		httpClient: &http.Client{Timeout: 30 * time.Second},
		baseURL:    cfg.CabyAI.BaseURL,
		apiKey:     cfg.CabyAI.APIKey,
	}
}

// RecognitionRequest 识别请求
type RecognitionRequest struct {
	UserID    string `json:"user_id"`
	ModelType string `json:"model_type"`
}

// RecognitionResponse 识别响应
type RecognitionResponse struct {
	Success       bool    `json:"success"`
	IsNewCat      bool    `json:"is_new_cat"`
	CatID         string  `json:"cat_id,omitempty"`
	CatName       string  `json:"cat_name,omitempty"`
	Confidence    float64 `json:"confidence"`
	Similarity    float64 `json:"similarity,omitempty"`
	ModelType     string  `json:"model_type"`
	ShadowMode    bool    `json:"shadow_mode,omitempty"`
	Message       string  `json:"message,omitempty"`
	Error         string  `json:"error,omitempty"`
	RequestID     string  `json:"request_id,omitempty"`
}

// RegisterCatRequest 注册猫咪请求
type RegisterCatRequest struct {
	UserID    string `json:"user_id"`
	CatID     string `json:"cat_id"`
	CatName   string `json:"cat_name"`
	ModelType string `json:"model_type"`
}

// RegisterCatResponse 注册猫咪响应
type RegisterCatResponse struct {
	Success   bool   `json:"success"`
	CatID     string `json:"cat_id,omitempty"`
	CatName   string `json:"cat_name,omitempty"`
	ModelType string `json:"model_type"`
	Message   string `json:"message,omitempty"`
	Error     string `json:"error,omitempty"`
	RequestID string `json:"request_id,omitempty"`
}

// RecognizeCat 识别猫咪个体
func (crc *CatRecognitionClient) RecognizeCat(userID string, imageData []byte, modelType string) (*RecognitionResponse, error) {
	url := fmt.Sprintf("%s/api/v1/recognition/identify", crc.baseURL)

	// 创建multipart表单
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加表单字段
	writer.WriteField("user_id", userID)
	if modelType != "" {
		writer.WriteField("model_type", modelType)
	}

	// 添加图像文件
	part, err := writer.CreateFormFile("image", "image.jpg")
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %w", err)
	}
	part.Write(imageData)

	writer.Close()

	// 创建请求
	req, err := http.NewRequest("POST", url, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", crc.apiKey))

	// 发送请求
	resp, err := crc.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode, string(body))
	}

	var result RecognitionResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &result, nil
}

// RegisterCat 注册新猫咪
func (crc *CatRecognitionClient) RegisterCat(userID, catID, catName string, imageData []byte, modelType string) (*RegisterCatResponse, error) {
	url := fmt.Sprintf("%s/api/v1/recognition/register", crc.baseURL)

	// 创建multipart表单
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加表单字段
	writer.WriteField("user_id", userID)
	writer.WriteField("cat_id", catID)
	writer.WriteField("cat_name", catName)
	if modelType != "" {
		writer.WriteField("model_type", modelType)
	}

	// 添加图像文件
	part, err := writer.CreateFormFile("image", "image.jpg")
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %w", err)
	}
	part.Write(imageData)

	writer.Close()

	// 创建请求
	req, err := http.NewRequest("POST", url, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", crc.apiKey))

	// 发送请求
	resp, err := crc.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode, string(body))
	}

	var result RegisterCatResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &result, nil
}

// CatRecognitionResult 个体识别结果
type CatRecognitionResult struct {
	IsNewCat         bool    `json:"is_new_cat"`
	CatID            string  `json:"cat_id,omitempty"`
	CatName          string  `json:"cat_name,omitempty"`
	OriginalResult   string  `json:"original_result,omitempty"`
	InfraredResult   string  `json:"infrared_result,omitempty"`
	OriginalConf     float64 `json:"original_confidence,omitempty"`
	InfraredConf     float64 `json:"infrared_confidence,omitempty"`
	Similarity       float64 `json:"similarity,omitempty"`
	ShadowModeActive bool    `json:"shadow_mode_active"`
	Message          string  `json:"message,omitempty"`
}

// ProcessCatRecognition 处理猫咪个体识别（同时调用两个模型）
func (crc *CatRecognitionClient) ProcessCatRecognition(userID string, imageData []byte) (*CatRecognitionResult, error) {
	result := &CatRecognitionResult{
		ShadowModeActive: true,
	}

	// 调用红外模型（主要模型）
	infraredResp, err := crc.RecognizeCat(userID, imageData, "infrared")
	if err != nil {
		return nil, fmt.Errorf("infrared recognition failed: %w", err)
	}

	// 调用原始模型（对比模型）
	originalResp, err := crc.RecognizeCat(userID, imageData, "original")
	if err != nil {
		// 原始模型失败不影响主流程，只记录
		result.Message = fmt.Sprintf("Original model failed: %v", err)
	}

	// 处理红外模型结果（主要结果）
	result.IsNewCat = infraredResp.IsNewCat
	result.CatID = infraredResp.CatID
	result.CatName = infraredResp.CatName
	result.InfraredResult = infraredResp.CatName
	result.InfraredConf = infraredResp.Confidence
	result.Similarity = infraredResp.Similarity

	// 处理原始模型结果（对比结果）
	if originalResp != nil && originalResp.Success {
		result.OriginalResult = originalResp.CatName
		result.OriginalConf = originalResp.Confidence
		
		// 构建影子模式消息
		if result.OriginalResult != result.InfraredResult {
			result.Message = fmt.Sprintf("Shadow mode: %s（%s）", 
				result.InfraredResult, result.OriginalResult)
		} else {
			result.Message = fmt.Sprintf("Both models agree: %s", result.InfraredResult)
		}
	} else {
		result.Message = fmt.Sprintf("Infrared result: %s", result.InfraredResult)
	}

	return result, nil
}

// GenerateNewCatID 生成新的cat_id
func (crc *CatRecognitionClient) GenerateNewCatID() string {
	return fmt.Sprintf("cat_%d", time.Now().UnixNano())
}

// GenerateNewCatName 生成新猫咪名称（NewCat<DateTime>格式）
func (crc *CatRecognitionClient) GenerateNewCatName() string {
	return fmt.Sprintf("NewCat%s", time.Now().Format("20060102150405"))
}
