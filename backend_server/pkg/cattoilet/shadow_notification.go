package cattoilet

import (
	"fmt"
	"log"
	"time"
)

// ShadowModeNotification 影子模式通知
type ShadowModeNotification struct {
	UserID           string    `json:"user_id"`
	OriginalResult   string    `json:"original_result"`
	InfraredResult   string    `json:"infrared_result"`
	OriginalConf     float64   `json:"original_confidence"`
	InfraredConf     float64   `json:"infrared_confidence"`
	IsConsistent     bool      `json:"is_consistent"`
	Message          string    `json:"message"`
	Timestamp        time.Time `json:"timestamp"`
}

// NotificationService 通知服务
type NotificationService struct {
	// 这里可以集成实际的推送服务，如APNs、FCM等
}

// NewNotificationService 创建通知服务
func NewNotificationService() *NotificationService {
	return &NotificationService{}
}

// SendShadowModeNotification 发送影子模式通知
func (ns *NotificationService) SendShadowModeNotification(userID string, result *CatRecognitionResult) {
	if !result.ShadowModeActive {
		return
	}

	notification := &ShadowModeNotification{
		UserID:         userID,
		OriginalResult: result.OriginalResult,
		InfraredResult: result.InfraredResult,
		OriginalConf:   result.OriginalConf,
		InfraredConf:   result.InfraredConf,
		IsConsistent:   result.OriginalResult == result.InfraredResult,
		Timestamp:      time.Now(),
	}

	// 构建消息内容
	if notification.IsConsistent {
		notification.Message = fmt.Sprintf("识别结果一致: %s", result.InfraredResult)
	} else {
		notification.Message = fmt.Sprintf("影子模式对比: %s（%s）", 
			result.InfraredResult, result.OriginalResult)
	}

	// 这里应该调用实际的推送服务
	// 目前只是记录日志
	log.Printf("Shadow mode notification for user %s: %s", userID, notification.Message)
	
	// TODO: 集成实际的推送服务
	// - APNs for iOS
	// - FCM for Android
	// - WebSocket for real-time web notifications
	// - Email notifications
	
	ns.logNotification(notification)
}

// logNotification 记录通知日志
func (ns *NotificationService) logNotification(notification *ShadowModeNotification) {
	log.Printf("Shadow Mode Notification:")
	log.Printf("  User ID: %s", notification.UserID)
	log.Printf("  Original Model: %s (%.4f)", notification.OriginalResult, notification.OriginalConf)
	log.Printf("  Infrared Model: %s (%.4f)", notification.InfraredResult, notification.InfraredConf)
	log.Printf("  Consistent: %v", notification.IsConsistent)
	log.Printf("  Message: %s", notification.Message)
	log.Printf("  Timestamp: %s", notification.Timestamp.Format(time.RFC3339))
}

// FormatShadowModeMessage 格式化影子模式消息（用于在标题后添加括号）
func FormatShadowModeMessage(originalTitle string, result *CatRecognitionResult) string {
	if !result.ShadowModeActive {
		return originalTitle
	}

	if result.OriginalResult == "" {
		// 只有红外结果
		return fmt.Sprintf("%s（红外识别）", originalTitle)
	}

	if result.OriginalResult == result.InfraredResult {
		// 两个模型结果一致
		return fmt.Sprintf("%s（%s）", originalTitle, result.InfraredResult)
	} else {
		// 两个模型结果不一致
		return fmt.Sprintf("%s（%s vs %s）", originalTitle, result.InfraredResult, result.OriginalResult)
	}
}

// ShadowModeStats 影子模式统计
type ShadowModeStats struct {
	TotalRequests     int     `json:"total_requests"`
	ConsistentResults int     `json:"consistent_results"`
	InconsistentResults int   `json:"inconsistent_results"`
	ConsistencyRate   float64 `json:"consistency_rate"`
	LastUpdated       time.Time `json:"last_updated"`
}

// UpdateShadowModeStats 更新影子模式统计
func (ns *NotificationService) UpdateShadowModeStats(result *CatRecognitionResult) *ShadowModeStats {
	// 这里应该从数据库或缓存中获取和更新统计信息
	// 目前只是示例实现
	
	stats := &ShadowModeStats{
		TotalRequests: 1,
		LastUpdated:   time.Now(),
	}

	if result.OriginalResult == result.InfraredResult {
		stats.ConsistentResults = 1
		stats.InconsistentResults = 0
	} else {
		stats.ConsistentResults = 0
		stats.InconsistentResults = 1
	}

	if stats.TotalRequests > 0 {
		stats.ConsistencyRate = float64(stats.ConsistentResults) / float64(stats.TotalRequests)
	}

	log.Printf("Shadow Mode Stats Updated:")
	log.Printf("  Total Requests: %d", stats.TotalRequests)
	log.Printf("  Consistent: %d", stats.ConsistentResults)
	log.Printf("  Inconsistent: %d", stats.InconsistentResults)
	log.Printf("  Consistency Rate: %.2f%%", stats.ConsistencyRate*100)

	return stats
}

// GetShadowModeReport 获取影子模式报告
func (ns *NotificationService) GetShadowModeReport(userID string, days int) map[string]interface{} {
	// 这里应该从数据库查询指定用户和时间范围的影子模式数据
	// 目前只是示例实现
	
	report := map[string]interface{}{
		"user_id":     userID,
		"period_days": days,
		"generated_at": time.Now(),
		"summary": map[string]interface{}{
			"total_recognitions": 0,
			"consistent_results": 0,
			"inconsistent_results": 0,
			"consistency_rate": 0.0,
		},
		"model_performance": map[string]interface{}{
			"infrared_model": map[string]interface{}{
				"avg_confidence": 0.0,
				"recognition_count": 0,
			},
			"original_model": map[string]interface{}{
				"avg_confidence": 0.0,
				"recognition_count": 0,
			},
		},
		"discrepancies": []map[string]interface{}{
			// 记录不一致的识别结果
		},
	}

	return report
}

// SendShadowModeReport 发送影子模式报告
func (ns *NotificationService) SendShadowModeReport(userID string, report map[string]interface{}) {
	log.Printf("Sending shadow mode report to user %s", userID)
	
	// TODO: 实现报告发送逻辑
	// - 生成PDF报告
	// - 发送邮件
	// - 推送通知
	// - 保存到用户dashboard
}

// EnableShadowModeForUser 为用户启用影子模式
func (ns *NotificationService) EnableShadowModeForUser(userID string) error {
	log.Printf("Enabling shadow mode for user: %s", userID)
	
	// TODO: 在数据库中记录用户的影子模式设置
	// - 用户偏好设置
	// - 通知频率设置
	// - 报告生成设置
	
	return nil
}

// DisableShadowModeForUser 为用户禁用影子模式
func (ns *NotificationService) DisableShadowModeForUser(userID string) error {
	log.Printf("Disabling shadow mode for user: %s", userID)
	
	// TODO: 在数据库中更新用户的影子模式设置
	
	return nil
}

// IsShadowModeEnabledForUser 检查用户是否启用了影子模式
func (ns *NotificationService) IsShadowModeEnabledForUser(userID string) bool {
	// TODO: 从数据库查询用户设置
	// 目前默认为所有用户启用
	return true
}
