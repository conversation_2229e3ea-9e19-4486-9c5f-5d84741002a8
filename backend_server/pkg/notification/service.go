package notification

import (
	"cabycare-server/config"
	"cabycare-server/pkg/cat"
	"cabycare-server/pkg/utils"
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"log"
	"time"
)

type NotificationService struct {
	db         *Database
	apns       *APNSProvider
	catService *cat.CatService
	// 预留其他推送服务提供商
	// fcm   *FCMProvider
	// huawei *HuaweiProvider
}

func NewNotificationService(cfg *config.Config) (*NotificationService, error) {
	db, err := NewDatabase(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create notification database: %v", err)
	}

	apns, err := NewAPNSProvider(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create APNS provider: %v", err)
	}

	catService, err := cat.NewCatService(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create cat service: %v", err)
	}

	return &NotificationService{
		db:         db,
		apns:       apns,
		catService: catService,
	}, nil
}

// CreateNotification 创建新通知
func (s *NotificationService) CreateNotification(ctx context.Context, notification *Notification) error {
	// 生成通知ID
	notification.NoticeID = generateNotificationID()

	// 验证通知类型
	if err := s.validateNotification(notification); err != nil {
		return err
	}

	// 从元数据中获取 cat_id
	var metadata map[string]interface{}
	if err := json.Unmarshal(notification.Metadata, &metadata); err != nil {
		log.Printf("Failed to unmarshal notification metadata: %v", err)
		return err
	}

	// 安全地获取 cat_id
	catID, ok := metadata["cat_id"].(string)
	if !ok {
		// 如果 cat_id 不存在或无效，使用默认的 unknown cat_id
		hasher := sha256.New()
		hasher.Write([]byte(notification.UserID + "_unknown"))
		catID = fmt.Sprintf("c_%x", hasher.Sum(nil)[:8])
		log.Printf("Using default unknown cat_id: %s for user: %s", catID, notification.UserID)
		metadata["cat_id"] = catID
		// 更新通知的元数据
		updatedMetadata, err := json.Marshal(metadata)
		if err != nil {
			log.Printf("Failed to marshal updated metadata: %v", err)
			return err
		}
		notification.Metadata = updatedMetadata
	}

	// 检查 cat 是否存在，如果不存在则创建
	_, err := s.catService.GetCat(catID)
	if err != nil {
		if err.Error() == "record not found" {
			// 创建未知猫咪及其档案
			if err := s.catService.CreateUnknownCat(catID, notification.UserID); err != nil {
				log.Printf("Failed to create unknown cat: %v", err)
				return err
			}
			log.Printf("Created unknown cat with ID: %s for user: %s", catID, notification.UserID)
		} else {
			log.Printf("Failed to check if cat exists: %v", err)
			return err
		}
	}

	// 检查用户通知设置
	if notification.Type == TypeDaily || notification.Type == TypeStats {
		settings, err := s.db.GetUserNotificationSettings(notification.UserID)
		if err != nil {
			return err
		}

		// 检查是否允许发送
		if !s.shouldSendNotification(notification, settings) {
			// 如果在免打扰时间内，保存到免打扰通知
			log.Printf("Notification %s should not be sent in quiet hours", notification.NoticeID)

			duration, ok := metadata["duration"].(float64)
			if !ok {
				log.Printf("Warning: duration not found in metadata or invalid type")
				duration = 0 // 使用默认值
			}

			quietNotification := &QuietNotification{
				UserID:   notification.UserID,
				CatID:    catID,
				Type:     string(notification.Type),
				SubType:  string(notification.SubType),
				Title:    notification.Title,
				Body:     notification.Body,
				Duration: int(duration),
				Count:    1,
			}
			if err := s.db.CreateQuietNotification(quietNotification); err != nil {
				log.Printf("Failed to save quiet notification: %v", err)
			}
			return nil
		}
	}

	// 保存通知到数据库
	if err := s.db.CreateNotification(notification); err != nil {
		return err
	}

	// 发送推送通知
	return s.sendNotification(ctx, notification)
}

// sendNotification 发送通知到所有设备
func (s *NotificationService) sendNotification(ctx context.Context, notification *Notification) error {
	// 获取用户的设备令牌
	tokens, err := s.db.GetUserDeviceTokens(notification.UserID)
	if err != nil {
		return err
	}

	// 发送推送通知
	var failedTokens []string
	for _, token := range tokens {
		switch token.Provider {
		case ProviderAPNS:
			if err := s.apns.Send(ctx, notification, token); err != nil {
				log.Printf("Failed to send APNS notification to device %s: %v", token.DeviceID, err)
				failedTokens = append(failedTokens, token.DeviceID)
			}
		// 预留其他推送服务
		// case ProviderFCM:
		//     s.fcm.Send(notification, token)
		}
	}

	// 如果所有设备都推送失败，删除通知
	if len(failedTokens) == len(tokens) {
		if err := s.db.DeleteNotification(notification.ID); err != nil {
			log.Printf("Failed to delete notification %s after all devices failed: %v", notification.NoticeID, err)
		}
		return fmt.Errorf("failed to send notification to all devices")
	}

	// 如果有部分设备推送失败，记录日志但不删除通知
	if len(failedTokens) > 0 {
		log.Printf("Notification %s partially sent: %d/%d devices failed",
			notification.NoticeID, len(failedTokens), len(tokens))
	}

	return nil
}

// generateNotificationID 生成通知ID
func generateNotificationID() string {
	return fmt.Sprintf("not_%d", time.Now().UnixNano())
}

// validateNotification 验证通知数据
func (s *NotificationService) validateNotification(n *Notification) error {
	if n.UserID == "" {
		return fmt.Errorf("user_id is required")
	}

	if n.Title == "" || n.Body == "" {
		return fmt.Errorf("title and body are required")
	}

	// 验证元数据
	if n.Metadata != nil {
		switch n.Type {
		case TypeDaily:
			var meta DailyMetadata
			jsonData, err := json.Marshal(n.Metadata)
			if err != nil {
				return fmt.Errorf("failed to marshal metadata: %v", err)
			}
			if err := json.Unmarshal(jsonData, &meta); err != nil {
				return fmt.Errorf("invalid daily metadata: %v", err)
			}
		case TypeAbnormal:
			var meta AbnormalMetadata
			jsonData, err := json.Marshal(n.Metadata)
			if err != nil {
				return fmt.Errorf("failed to marshal metadata: %v", err)
			}
			if err := json.Unmarshal(jsonData, &meta); err != nil {
				return fmt.Errorf("invalid abnormal metadata: %v", err)
			}
		// ... 其他类型的验证
		}
	}

	return nil
}

// shouldSendNotification 检查是否应该发送通知
func (s *NotificationService) shouldSendNotification(notification *Notification, settings *NotificationSettings) bool {
	// 检查通知类型是否启用
	if notification.Type == TypeDaily && !settings.EnableDaily {
		return false
	}
	if notification.Type == TypeStats && !settings.EnableStats {
		return false
	}

	// 获取用户时区
	loc, err := time.LoadLocation(settings.Timezone)
	if err != nil {
		// 如果时区加载失败，尝试使用 IANA 时区数据库
		loc, err = time.LoadLocation("UTC")
		if err != nil {
			log.Printf("Failed to load timezone %s and UTC: %v", settings.Timezone, err)
			return true // 如果时区加载失败，默认发送通知
		}
		log.Printf("Using UTC timezone as fallback for %s", settings.Timezone)
	}

	// 获取用户时区的当前时间
	now := time.Now().In(loc)
	currentMinutes := now.Hour()*60 + now.Minute()

	// 检查是否在免打扰时间内
	if settings.QuietHoursStart > settings.QuietHoursEnd {
		// 跨天的情况，例如 22:00 - 07:00
		if currentMinutes >= settings.QuietHoursStart || currentMinutes < settings.QuietHoursEnd {
			return false
		}
	} else {
		// 同一天的情况，例如 22:00 - 23:00
		if currentMinutes >= settings.QuietHoursStart && currentMinutes < settings.QuietHoursEnd {
			return false
		}
	}

	return true
}

// UpdateNotificationStatus 更新通知状态
func (s *NotificationService) UpdateNotificationStatus(id string, status NotificationStatus) error {
	return s.db.UpdateNotificationStatus(id, status)
}

// GetUserNotifications 获取用户的通知列表
func (s *NotificationService) GetUserNotifications(userID string, limit, offset int) ([]Notification, error) {
	return s.db.GetUserNotifications(userID, limit, offset)
}

// MarkNotificationAsRead 标记通知为已读
func (s *NotificationService) MarkNotificationAsRead(id string) error {
	return s.db.MarkNotificationAsRead(id)
}

// GetUserNotificationSettings 获取用户通知设置
func (s *NotificationService) GetUserNotificationSettings(userID string) (*NotificationSettings, error) {
	return s.db.GetUserNotificationSettings(userID)
}

// validateTimezone 验证时区是否有效
func validateTimezone(timezone string) bool {
	_, err := time.LoadLocation(timezone)
	return err == nil
}

// UpdateUserNotificationSettings 更新用户通知设置
func (s *NotificationService) UpdateUserNotificationSettings(settings *NotificationSettings) error {
	// 验证时区
	if !validateTimezone(settings.Timezone) {
		return fmt.Errorf("invalid timezone: %s", settings.Timezone)
	}
	return s.db.UpdateUserNotificationSettings(settings)
}

// RegisterDeviceToken 注册设备推送令牌
func (s *NotificationService) RegisterDeviceToken(token *DeviceToken) error {
	return s.db.SaveDeviceToken(token)
}

// UnregisterDeviceToken 注销设备推送令牌
func (s *NotificationService) UnregisterDeviceToken(deviceID string) error {
	return s.db.DeleteDeviceToken(deviceID)
}

// GetNotification 获取单个通知
func (s *NotificationService) GetNotification(id string) (*Notification, error) {
	return s.db.GetNotification(id)
}

// StorePendingNotification 存储待处理的通知
func (s *NotificationService) StorePendingNotification(videoID string, notification *Notification) error {
	jsonData, err := json.Marshal(notification.Metadata)
	if err != nil {
		return fmt.Errorf("failed to marshal metadata: %v", err)
	}
	pending := &PendingNotification{
		VideoID:   videoID,
		Type:      notification.Type,
		SubType:   notification.SubType,
		UserID:    notification.UserID,
		Title:     notification.Title,
		Body:      notification.Body,
		Priority:  notification.Priority,
		Metadata:  jsonData,
		CreatedAt: time.Now(),
	}
	return s.db.StorePendingNotification(pending)
}

// GetPendingNotification 获取待处理的通知
func (s *NotificationService) GetPendingNotification(videoID string) (*Notification, error) {
	pending, err := s.db.GetPendingNotification(videoID)
	if err != nil {
		return nil, err
	}
	return &Notification{
		Type:     pending.Type,
		SubType:  pending.SubType,
		UserID:   pending.UserID,
		Title:    pending.Title,
		Body:     pending.Body,
		Priority: pending.Priority,
		Metadata: pending.Metadata,
	}, nil
}

// DeletePendingNotification 删除待处理的通知
func (s *NotificationService) DeletePendingNotification(videoID string) error {
	return s.db.DeletePendingNotification(videoID)
}

// ProcessQuietNotifications 处理免打扰期间积累的通知
func (s *NotificationService) ProcessQuietNotifications(ctx context.Context) error {
	// 获取所有待处理的通知
	quietNotifications, err := s.db.GetQuietNotifications()
	if err != nil {
		return fmt.Errorf("failed to get quiet notifications: %v", err)
	}

	// 按用户和猫咪分组处理通知
	userCatMap := make(map[string]map[string][]*QuietNotification)
	for i := range quietNotifications {
		qn := &quietNotifications[i]
		if _, ok := userCatMap[qn.UserID]; !ok {
			userCatMap[qn.UserID] = make(map[string][]*QuietNotification)
		}
		userCatMap[qn.UserID][qn.CatID] = append(userCatMap[qn.UserID][qn.CatID], qn)
	}

	// 处理每个用户的通知
	for userID, catMap := range userCatMap {
		// 获取用户通知设置
		settings, err := s.db.GetUserNotificationSettings(userID)
		if err != nil {
			log.Printf("Failed to get notification settings for user %s: %v", userID, err)
			continue
		}

		// 检查是否在免打扰时间
		if !s.shouldSendNotification(&Notification{UserID: userID}, settings) {
			continue
		}

		// 处理每只猫咪的通知
		for catID, notifications := range catMap {
			// 计算总次数和总时长
			totalCount := len(notifications)
			var totalDuration int
			for _, qn := range notifications {
				totalDuration += qn.Duration
			}

			// 创建合并后的通知
			metadata := map[string]interface{}{
				"cat_id": catID,
			}
			metadataJSON, err := json.Marshal(metadata)
			if err != nil {
				log.Printf("Failed to marshal metadata: %v", err)
				continue
			}

			// 格式化时间
			formattedDuration := utils.FormatDuration(totalDuration)
			mergedNotification := &Notification{
				UserID:   userID,
				Type:     NotificationType(notifications[0].Type),
				SubType:  NotificationSubType(notifications[0].SubType),
				Title:    notifications[0].Title,
				Body:     fmt.Sprintf("本喵在你休息的时候上了%d次厕所，总共花了%s，记得铲屎！", totalCount, formattedDuration),
				Priority: 1,
				Metadata: metadataJSON,
			}

			// 发送通知
			if err := s.sendNotification(ctx, mergedNotification); err != nil {
				log.Printf("Failed to send merged notification: %v", err)
				continue
			}

			// 删除所有已处理的通知
			for _, qn := range notifications {
				if err := s.db.DeleteQuietNotification(qn.ID); err != nil {
					log.Printf("Failed to delete quiet notification %d: %v", qn.ID, err)
				}
			}
		}
	}

	return nil
}

// StartQuietNotificationProcessor 启动免打扰通知处理器
func (s *NotificationService) StartQuietNotificationProcessor(ctx context.Context) {
	go func() {
		ticker := time.NewTicker(5 * time.Minute)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				if err := s.ProcessQuietNotifications(ctx); err != nil {
					log.Printf("Failed to process quiet notifications: %v", err)
				}
			}
		}
	}()
}

// Notification cleaner, it cleans pending notification and quiet notification outdated for 7 days
func (s *NotificationService) CleanOutDatedNotifications(ctx context.Context) error {
	// Calculate the cutoff time (7 days ago)
	cutoffTime := time.Now().AddDate(0, 0, -7)

	// Clean outdated pending notifications
	pendingCount, err := s.db.DeletePendingNotificationsOlderThan(cutoffTime)
	if err != nil {
		log.Printf("Failed to clean outdated pending notifications: %v", err)
		return err
	}

	// Clean outdated quiet notifications
	quietCount, err := s.db.DeleteQuietNotificationsOlderThan(cutoffTime)
	if err != nil {
		log.Printf("Failed to clean outdated quiet notifications: %v", err)
		return err
	}

	log.Printf("Notification cleanup completed: deleted %d pending notifications and %d quiet notifications older than %s",
		pendingCount, quietCount, cutoffTime.Format(time.RFC3339))

	return nil
}

// StartWeeklyCleanup starts a goroutine that cleans outdated notifications once per week
func (s *NotificationService) StartWeeklyCleanup(ctx context.Context) {
	go func() {
		// Create a ticker for weekly cleanup (7 days = 168 hours)
		ticker := time.NewTicker(168 * time.Hour)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				if err := s.CleanOutDatedNotifications(ctx); err != nil {
					log.Printf("Weekly notification cleanup failed: %v", err)
				}
			}
		}
	}()
	log.Printf("Weekly notification cleanup scheduler started")
}

