package api

import (
	"cabycare-server/pkg/cattoilet"

	"github.com/gin-gonic/gin"
)

func RegisterCatsAuthRoutes(routerGroup *gin.RouterGroup, catHandler *cattoilet.Handler) {
	cats := routerGroup.Group("/cats")
	{
		cats.POST("", catHandler.CreateCat)
		cats.GET("/:cat_id", catHandler.GetCat)
		cats.PUT("/:cat_id", catHandler.UpdateCat)
		cats.DELETE("/:cat_id", catHandler.DeleteCat)
		cats.PUT("/:cat_id/hide", catHandler.HideCat)
		cats.PUT("/:cat_id/restore", catHandler.RestoreCat)

		cats.GET("", catHandler.ListUserCats)
		cats.GET("/hidden", catHandler.ListUserHiddenCats)
		cats.GET("/all", catHandler.ListUserAllCats)

		cats.GET("/:cat_id/metrics/daily", catHandler.GetCatDailyMetrics)
		cats.GET("/:cat_id/metrics/monthly", catHandler.GetCatMonthlyMetrics)
		cats.GET("/:cat_id/alerts", catHandler.ListCatAlerts)

		// 个体识别相关端点
		cats.POST("/recognize", catHandler.HandleCatRecognition)
	}
}
