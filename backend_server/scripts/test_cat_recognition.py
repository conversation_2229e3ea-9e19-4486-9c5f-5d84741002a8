#!/usr/bin/env python3
"""
测试猫咪个体识别系统的完整链路
"""

import requests
import base64
import json
import sys
import os
import time
from pathlib import Path

# API配置
BACKEND_API_BASE = "https://api.caby.care"
CABY_AI_BASE = "http://localhost:8765"
CABY_VISION_BASE = "http://localhost:8001"

# 测试用的token（需要替换为实际的token）
BACKEND_TOKEN = "your_backend_token_here"
CABY_AI_TOKEN = "caby-token"
CABY_VISION_TOKEN = "default_api_key"

def encode_image_to_base64(image_path):
    """将图像文件编码为base64"""
    with open(image_path, 'rb') as f:
        return base64.b64encode(f.read()).decode('utf-8')

def test_caby_vision_direct(image_path, task="predict"):
    """直接测试caby_vision API"""
    print(f"\n=== 测试 Caby Vision API ({task}) ===")
    
    url = f"{CABY_VISION_BASE}/{task}"
    headers = {
        "Authorization": f"Bearer {CABY_VISION_TOKEN}",
        "Content-Type": "application/json"
    }
    
    image_base64 = encode_image_to_base64(image_path)
    payload = {
        "image": image_base64,
        "return_features": True,
        "return_confidence": True,
        "task": task
    }
    
    response = requests.post(url, headers=headers, json=payload)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Success: {result.get('success', False)}")
        if result.get('success'):
            results = result.get('results', {})
            print(f"Predicted Cat: {results.get('predicted_cat')}")
            print(f"Confidence: {results.get('confidence', 0):.4f}")
            
            # 检查影子模式结果
            shadow_result = results.get('shadow_result')
            if shadow_result:
                print(f"Shadow Mode - Predicted Cat: {shadow_result.get('predicted_cat')}")
                print(f"Shadow Mode - Confidence: {shadow_result.get('confidence', 0):.4f}")
                print(f"Shadow Mode - Model Type: {shadow_result.get('model_type')}")
        else:
            print(f"Error: {result.get('error')}")
    else:
        print(f"Error: {response.text}")
    
    return response.status_code == 200

def test_caby_ai_recognition(image_path, user_id, model_type="infrared"):
    """测试caby_ai个体识别API"""
    print(f"\n=== 测试 Caby AI 个体识别 ({model_type}) ===")
    
    url = f"{CABY_AI_BASE}/api/v1/recognition/identify"
    headers = {
        "Authorization": f"Bearer {CABY_AI_TOKEN}"
    }
    
    with open(image_path, 'rb') as f:
        files = {
            'image': (os.path.basename(image_path), f, 'image/jpeg')
        }
        data = {
            'user_id': user_id,
            'model_type': model_type
        }
        
        response = requests.post(url, headers=headers, files=files, data=data)
    
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Success: {result.get('success', False)}")
        if result.get('success'):
            print(f"Is New Cat: {result.get('is_new_cat')}")
            print(f"Cat ID: {result.get('cat_id')}")
            print(f"Cat Name: {result.get('cat_name')}")
            print(f"Confidence: {result.get('confidence', 0):.4f}")
            print(f"Similarity: {result.get('similarity', 0):.4f}")
            print(f"Model Type: {result.get('model_type')}")
            print(f"Shadow Mode: {result.get('shadow_mode')}")
        else:
            print(f"Error: {result.get('error')}")
    else:
        print(f"Error: {response.text}")
    
    return response.status_code == 200

def test_backend_cat_recognition(image_path, user_token):
    """测试backend_server猫咪识别API"""
    print(f"\n=== 测试 Backend Server 猫咪识别 ===")
    
    url = f"{BACKEND_API_BASE}/api/cats/recognize"
    headers = {
        "Authorization": f"Bearer {user_token}"
    }
    
    with open(image_path, 'rb') as f:
        files = {
            'image': (os.path.basename(image_path), f, 'image/jpeg')
        }
        
        response = requests.post(url, headers=headers, files=files)
    
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Success: {result.get('success', False)}")
        if result.get('success'):
            cat_result = result.get('result', {})
            print(f"Is New Cat: {cat_result.get('is_new_cat')}")
            print(f"Cat ID: {cat_result.get('cat_id')}")
            print(f"Cat Name: {cat_result.get('cat_name')}")
            print(f"Original Result: {cat_result.get('original_result')}")
            print(f"Infrared Result: {cat_result.get('infrared_result')}")
            print(f"Original Confidence: {cat_result.get('original_confidence', 0):.4f}")
            print(f"Infrared Confidence: {cat_result.get('infrared_confidence', 0):.4f}")
            print(f"Shadow Mode Active: {cat_result.get('shadow_mode_active')}")
            print(f"Message: {cat_result.get('message')}")
            print(f"Response Message: {result.get('message')}")
        else:
            print(f"Error: {result.get('error')}")
    else:
        print(f"Error: {response.text}")
    
    return response.status_code == 200

def test_caby_ai_register_cat(image_path, user_id, cat_id, cat_name, model_type="infrared"):
    """测试caby_ai猫咪注册API"""
    print(f"\n=== 测试 Caby AI 猫咪注册 ({model_type}) ===")
    
    url = f"{CABY_AI_BASE}/api/v1/recognition/register"
    headers = {
        "Authorization": f"Bearer {CABY_AI_TOKEN}"
    }
    
    with open(image_path, 'rb') as f:
        files = {
            'image': (os.path.basename(image_path), f, 'image/jpeg')
        }
        data = {
            'user_id': user_id,
            'cat_id': cat_id,
            'cat_name': cat_name,
            'model_type': model_type
        }
        
        response = requests.post(url, headers=headers, files=files, data=data)
    
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Success: {result.get('success', False)}")
        if result.get('success'):
            print(f"Cat ID: {result.get('cat_id')}")
            print(f"Cat Name: {result.get('cat_name')}")
            print(f"Model Type: {result.get('model_type')}")
            print(f"Message: {result.get('message')}")
        else:
            print(f"Error: {result.get('error')}")
    else:
        print(f"Error: {response.text}")
    
    return response.status_code == 200

def run_full_test_suite(image_path, user_id, user_token):
    """运行完整的测试套件"""
    print("=" * 60)
    print("猫咪个体识别系统完整测试")
    print("=" * 60)
    
    if not os.path.exists(image_path):
        print(f"错误：图像文件不存在 - {image_path}")
        return False
    
    print(f"测试图像: {image_path}")
    print(f"用户ID: {user_id}")
    
    # 1. 测试caby_vision直接调用
    print("\n" + "=" * 40)
    print("第一步：测试 Caby Vision 服务")
    print("=" * 40)
    
    vision_original_ok = test_caby_vision_direct(image_path, "predict")
    vision_infrared_ok = test_caby_vision_direct(image_path, "predict_infrared")
    
    if not (vision_original_ok and vision_infrared_ok):
        print("❌ Caby Vision 测试失败，停止后续测试")
        return False
    
    print("✅ Caby Vision 测试通过")
    
    # 2. 测试caby_ai个体识别
    print("\n" + "=" * 40)
    print("第二步：测试 Caby AI 个体识别")
    print("=" * 40)
    
    ai_infrared_ok = test_caby_ai_recognition(image_path, user_id, "infrared")
    ai_original_ok = test_caby_ai_recognition(image_path, user_id, "original")
    
    if not (ai_infrared_ok and ai_original_ok):
        print("❌ Caby AI 个体识别测试失败，停止后续测试")
        return False
    
    print("✅ Caby AI 个体识别测试通过")
    
    # 3. 测试backend_server完整链路
    print("\n" + "=" * 40)
    print("第三步：测试 Backend Server 完整链路")
    print("=" * 40)
    
    if user_token == "your_backend_token_here":
        print("⚠️  跳过 Backend Server 测试（需要有效的用户token）")
    else:
        backend_ok = test_backend_cat_recognition(image_path, user_token)
        if not backend_ok:
            print("❌ Backend Server 测试失败")
            return False
        print("✅ Backend Server 测试通过")
    
    print("\n" + "=" * 60)
    print("🎉 所有测试完成！影子模式部署成功！")
    print("=" * 60)
    
    return True

def main():
    if len(sys.argv) < 3:
        print("用法: python test_cat_recognition.py <image_path> <user_id> [user_token]")
        print("示例: python test_cat_recognition.py cat.jpg test_user_123 your_token_here")
        return
    
    image_path = sys.argv[1]
    user_id = sys.argv[2]
    user_token = sys.argv[3] if len(sys.argv) > 3 else "your_backend_token_here"
    
    try:
        success = run_full_test_suite(image_path, user_id, user_token)
        if success:
            print("\n✅ 测试成功完成")
        else:
            print("\n❌ 测试失败")
            sys.exit(1)
    
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
