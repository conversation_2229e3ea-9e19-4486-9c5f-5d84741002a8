# CabyCare Backend API 文档

## 基本信息

- **基础URL**: `http://your-server.com/api`
- **认证方式**: <PERSON><PERSON> (通过Logto认证)
- **数据格式**: JSON
- **编码**: UTF-8

## 认证相关

### 1. 用户注册
```
POST /api/register
```

**请求体:**
```json
{
  "username": "string",
  "password": "string",
  "email": "string",
  "phone": "string",
  "nickname": "string"
}
```

**响应:**
```json
{
  "user_id": "string",
  "username": "string",
  "email": "string",
  "nickname": "string",
  "status": 1,
  "created_at": "2024-01-01T00:00:00Z"
}
```

### 2. 用户登录
```
POST /api/login
```

**请求体:**
```json
{
  "username": "string",
  "password": "string"
}
```

**响应:**
```json
{
  "user_id": "string",
  "username": "string",
  "email": "string",
  "nickname": "string",
  "last_login": "2024-01-01T00:00:00Z"
}
```

## 猫咪管理

### 1. 创建猫咪
```
POST /api/cats
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "name": "string",
  "gender": 1,
  "birthday": "2020-01-01",
  "weight": 4.5,
  "breed": "string",
  "color": "string",
  "photos_base64": ["base64_string"]
}
```

**性别编码说明:**
- `0`: 未知
- `1`: 雄性/未知
- `-1`: 雌性/未知
- `10`: 雄性/已绝育
- `11`: 雄性/未绝育
- `-10`: 雌性/已绝育
- `-11`: 雌性/未绝育

**响应:**
```json
{
  "status": "success",
  "message": "Cat created successfully",
  "data": {
    "cat_id": "string",
    "user_id": "string",
    "name": "string",
    "gender": 1,
    "birthday": "2020-01-01T00:00:00Z",
    "breed": "string",
    "color": "string",
    "weight": 4.5,
    "avatar_url": "string",
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

**说明:**
- 如果在 `photos_base64` 中提供了图片，第一张图片会被自动处理为猫咪头像
- 头像会被裁剪成方形，调整为512x512像素，并转换为JPEG格式
- `avatar_url` 字段包含头像的完整访问URL（需要认证）

### 2. 获取猫咪信息
```
GET /api/cats/{cat_id}
```

**路径参数:**
- `cat_id`: 猫咪ID

**响应:**
```json
{
  "cat_id": "string",
  "user_id": "string",
  "name": "string",
  "gender": 1,
  "birthday": "2020-01-01T00:00:00Z",
  "breed": "string",
  "color": "string",
  "weight": 4.5,
  "avatar_url": "string",
  "status": 1,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### 3. 更新猫咪信息
```
PUT /api/cats/{cat_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

**路径参数:**
- `cat_id`: 猫咪ID

**请求体:**
```json
{
  "name": "string",
  "status": 1
}
```

**响应:**
```json
{
  "cat_id": "string",
  "name": "string",
  "status": 1,
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### 4. 删除猫咪（软删除）
```
DELETE /api/cats/{cat_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

**路径参数:**
- `cat_id`: 猫咪ID

**响应:**
```json
{
  "status": "success",
  "message": "Cat deleted successfully",
  "cat_id": "string"
}
```

### 5. 隐藏猫咪
```
PUT /api/cats/{cat_id}/hide
```

**请求头:**
```
Authorization: Bearer <token>
```

**路径参数:**
- `cat_id`: 猫咪ID

**响应:**
```json
{
  "status": "success",
  "message": "Cat hidden successfully",
  "cat_id": "string"
}
```

### 6. 恢复猫咪（取消隐藏）
```
PUT /api/cats/{cat_id}/restore
```

**请求头:**
```
Authorization: Bearer <token>
```

**路径参数:**
- `cat_id`: 猫咪ID

**响应:**
```json
{
  "status": "success",
  "message": "Cat restored successfully",
  "cat_id": "string"
}
```

### 7. 获取用户猫咪列表（仅正常状态）
```
GET /api/cats
```

**请求头:**
```
Authorization: Bearer <token>
```

**响应:**
```json
[
  {
    "cat_id": "string",
    "user_id": "string",
    "name": "string",
    "gender": 1,
    "birthday": "2020-01-01T00:00:00Z",
    "breed": "string",
    "color": "string",
    "weight": 4.5,
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
]
```

### 8. 获取用户隐藏的猫咪列表
```
GET /api/cats/hidden
```

**请求头:**
```
Authorization: Bearer <token>
```

**响应:**
```json
[
  {
    "cat_id": "string",
    "user_id": "string",
    "name": "string",
    "gender": 1,
    "birthday": "2020-01-01T00:00:00Z",
    "breed": "string",
    "color": "string",
    "weight": 4.5,
    "status": 0,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
]
```

### 9. 获取用户所有猫咪列表（包括隐藏，不包括已删除）
```
GET /api/cats/all
```

**请求头:**
```
Authorization: Bearer <token>
```

**响应:**
```json
[
  {
    "cat_id": "string",
    "user_id": "string",
    "name": "string",
    "gender": 1,
    "birthday": "2020-01-01T00:00:00Z",
    "breed": "string",
    "color": "string",
    "weight": 4.5,
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
]
```

## 猫咪状态说明

- `status = 1`: 正常显示（默认状态）
- `status = 0`: 隐藏（不在常规列表中显示，但数据仍然存在）
- `status = -1`: 已删除（软删除，不在任何列表中显示）

## 统计相关

### 1. 获取猫咪每日统计
```
GET /api/cats/{cat_id}/metrics/daily?date=2024-01-01
```

### 2. 获取猫咪月度统计
```
GET /api/cats/{cat_id}/metrics/monthly?year=2024&month=1
```

### 3. 获取猫咪健康警报
```
GET /api/cats/{cat_id}/alerts?status=1
```

## 设备管理

### 1. 注册设备
```
POST /api/devices
```

### 2. 设备心跳
```
POST /api/devices/heartbeat
```

### 3. 获取设备信息
```
GET /api/devices/{device_id}
```

### 4. 获取用户设备列表
```
GET /api/users/{user_id}/devices
```

## 视频记录

### 1. 创建视频记录
```
POST /api/records
```

### 2. 获取视频记录列表
```
GET /api/records?user_id=xxx&device_id=xxx&start_time=xxx&end_time=xxx
```

## 家庭组管理

### 1. 创建家庭组
```
POST /api/family-groups
```

### 2. 获取家庭组信息
```
GET /api/family-groups/{group_id}
```

### 3. 添加家庭组成员
```
POST /api/family-groups/{group_id}/members
```

### 4. 添加家庭组设备
```
POST /api/family-groups/{group_id}/devices
```

## 通知管理

### 1. 获取用户通知列表
```
GET /api/users/{user_id}/notifications?is_read=false
```

### 2. 注册客户端推送Token
```
POST /api/clients/tokens
```

## 错误响应格式

```json
{
  "error": "error_message",
  "code": 400
}
```

## 常见HTTP状态码

- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 存储资源访问

### 头像资源访问
```
GET /api/storage/assets/avatars/cats/{cat_id}.jpg
```

**请求头:**
```
Authorization: Bearer <token>
```

**说明:**
- 所有存储资源都需要认证访问
- 头像URL会在获取猫咪信息的接口中自动包含
- 无需手动构建存储资源URL

## 注意事项

1. 所有需要认证的接口都必须在请求头中包含 `Authorization: Bearer <token>`
2. 时间格式统一使用 RFC3339 格式: `2006-01-02T15:04:05Z07:00`
3. 猫咪删除使用软删除机制，数据仍保留在数据库中
4. 隐藏功能适用于猫咪暂时离开或不想在常规列表中显示的场景
5. 所有涉及用户数据的操作都会验证用户权限
6. 存储资源（如头像）都需要认证访问，无公开访问权限 