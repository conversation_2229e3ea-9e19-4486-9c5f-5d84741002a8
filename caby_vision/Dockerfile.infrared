# Dockerfile for Caby Vision with Infrared Cat Recognition
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 复制 requirements
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 安装红外模型相关依赖
RUN pip install --no-cache-dir \
    onnxruntime \
    scikit-learn \
    pyyaml

# 复制应用代码
COPY . .

# 创建模型目录
RUN mkdir -p models/infrared config logs

# 设置环境变量
ENV PYTHONPATH=/app
ENV INFRARED_DEVICE=cpu

# 暴露端口
EXPOSE 8001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8001/health')"

# 启动命令
CMD ["python", "triton_service/triton_server.py"]
