# Triton Server Python Backend Dependencies
tritonclient[all]>=2.40.0
numpy>=1.24.0,<2.4.0

# Computer Vision
opencv-python-headless>=4.8.0,<5.0.0
Pillow>=10.0.0,<12.0.0
scikit-image>=0.21.0,<1.0.0

# Scientific Computing  
scipy>=1.11.0,<2.0.0

# Configuration and data handling
PyYAML>=6.0

# Web框架 (用于API代理)
fastapi>=0.104.0,<1.0.0
uvicorn>=0.24.0,<1.0.0
python-multipart>=0.0.6,<1.0.0

# Utilities
pydantic>=2.4.0,<3.0.0
requests>=2.31.0,<3.0.0
tqdm>=4.66.1,<5.0.0

# Optional: 可视化库
matplotlib>=3.7.2,<4.0.0
seaborn>=0.12.2,<1.0.0

# thop: 模型复杂度分析
thop==0.1.1.post2209072238

# PyTorch GPU版本
torch>=2.1.0,<2.8.0
torchvision>=0.16.0,<0.23.0
torchaudio>=2.1.0,<2.8.0

# TIMM for MegaDescriptor model
timm>=0.9.12,<1.0.0

# Scikit-learn for calibration
scikit-learn>=1.3.0,<2.0.0

# Additional dependencies for Reid model
transformers>=4.30.0,<5.0.0
