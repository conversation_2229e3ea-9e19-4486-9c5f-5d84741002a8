#!/usr/bin/env python3
"""
红外猫咪识别检测器适配器
将红外猫咪识别模型集成到caby_vision中作为影子模式
"""

import os
import sys
import logging
import base64
import io
from typing import Dict, Any, Optional, Union, Tuple
import numpy as np
from PIL import Image
import torch

# 导入配置管理器
from infrared_config import get_infrared_config

# 添加红外猫咪识别模型路径
config = get_infrared_config()
infrared_models_path = os.path.dirname(config.get_model_path())
sys.path.insert(0, infrared_models_path)

try:
    from infrared_cat_recognizer import InfraredCatRecognizer
except ImportError as e:
    logging.error(f"Failed to import InfraredCatRecognizer: {e}")
    InfraredCatRecognizer = None

logger = logging.getLogger(__name__)

class InfraredCatDetector:
    """红外猫咪识别检测器适配器"""
    
    def __init__(self,
                 model_path: str = None,
                 reference_features_path: str = None,
                 device: str = "auto"):
        """
        初始化红外猫咪检测器

        Args:
            model_path: ONNX模型路径
            reference_features_path: 参考特征数据库路径
            device: 运行设备 ("auto", "cpu", "cuda")
        """
        # 获取配置
        self.config = get_infrared_config()

        # 使用配置或参数
        self.device = device if device != "auto" else self.config.get_device()
        self.model_path = model_path or self.config.get_model_path()
        self.reference_features_path = reference_features_path or self.config.get_reference_features_path()
        self.recognizer = None
        self.is_loaded = False
        
        # 检查文件是否存在
        if not os.path.exists(self.model_path):
            logger.error(f"Model file not found: {self.model_path}")
            return
            
        if not os.path.exists(self.reference_features_path):
            logger.error(f"Reference features file not found: {self.reference_features_path}")
            return
        
        # 初始化识别器
        self._load_model()
    
    def _load_model(self):
        """加载模型"""
        try:
            if InfraredCatRecognizer is None:
                logger.error("InfraredCatRecognizer not available")
                return
                
            self.recognizer = InfraredCatRecognizer(
                onnx_model_path=self.model_path,
                reference_features_path=self.reference_features_path,
                device=self.device
            )
            self.is_loaded = True
            logger.info(f"Infrared cat recognizer loaded successfully with device: {self.device}")
            
        except Exception as e:
            logger.error(f"Failed to load infrared cat recognizer: {e}")
            self.is_loaded = False
    
    def predict_image(self, 
                     image: np.ndarray, 
                     return_features: bool = False,
                     return_confidence: bool = True) -> Dict:
        """
        预测图像中的猫咪个体（红外模式）
        
        Args:
            image: 输入图像 (BGR格式)
            return_features: 是否返回特征向量
            return_confidence: 是否返回置信度
            
        Returns:
            包含预测结果的字典
        """
        if not self.is_loaded or self.recognizer is None:
            return {
                "error": "Infrared model not loaded",
                "predicted_cat": None,
                "confidence": 0.0,
                "features": None,
                "shadow_mode": True
            }
        
        try:
            # 转换BGR到RGB
            image_rgb = image[:, :, ::-1] if len(image.shape) == 3 else image
            
            # 预测
            predicted_cat, confidence = self.recognizer.predict(image_rgb)
            
            result = {
                "predicted_cat": predicted_cat,
                "confidence": float(confidence),
                "shadow_mode": True,
                "model_type": "infrared"
            }
            
            # 如果需要返回特征
            if return_features:
                try:
                    features = self.recognizer.extract_features(image_rgb)
                    result["features"] = features.tolist()
                    result["feature_dim"] = len(features)
                except Exception as e:
                    logger.error(f"Failed to extract features: {e}")
                    result["features"] = None
                    result["feature_dim"] = 0
            
            return result
            
        except Exception as e:
            logger.error(f"Infrared prediction error: {str(e)}")
            return {
                "error": f"Failed to process image: {str(e)}",
                "predicted_cat": None,
                "confidence": 0.0,
                "features": None,
                "shadow_mode": True
            }
    
    def predict_from_base64(self, 
                           image_base64: str, 
                           return_features: bool = False,
                           return_confidence: bool = True) -> Dict:
        """
        从base64编码的图像进行预测（红外模式）
        
        Args:
            image_base64: base64编码的图像
            return_features: 是否返回特征向量
            return_confidence: 是否返回置信度
            
        Returns:
            包含预测结果的字典
        """
        if not self.is_loaded or self.recognizer is None:
            return {
                "error": "Infrared model not loaded",
                "predicted_cat": None,
                "confidence": 0.0,
                "features": None,
                "shadow_mode": True
            }
        
        try:
            # 预测
            predicted_cat, confidence = self.recognizer.predict_from_base64(image_base64)
            
            result = {
                "predicted_cat": predicted_cat,
                "confidence": float(confidence),
                "shadow_mode": True,
                "model_type": "infrared"
            }
            
            # 如果需要返回特征
            if return_features:
                try:
                    features = self.recognizer.extract_features_from_base64(image_base64)
                    result["features"] = features.tolist()
                    result["feature_dim"] = len(features)
                except Exception as e:
                    logger.error(f"Failed to extract features: {e}")
                    result["features"] = None
                    result["feature_dim"] = 0
            
            return result
            
        except Exception as e:
            logger.error(f"Base64 infrared prediction error: {str(e)}")
            return {
                "error": f"Failed to process base64 image: {str(e)}",
                "predicted_cat": None,
                "confidence": 0.0,
                "features": None,
                "shadow_mode": True
            }
    
    def get_cat_features(self, image: np.ndarray) -> Dict:
        """
        提取猫咪特征向量（红外模式）
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            包含特征向量的字典
        """
        if not self.is_loaded or self.recognizer is None:
            return {
                "error": "Infrared model not loaded",
                "features": None,
                "feature_dim": 0,
                "shadow_mode": True
            }
        
        try:
            # 转换BGR到RGB
            image_rgb = image[:, :, ::-1] if len(image.shape) == 3 else image
            
            # 提取特征
            features = self.recognizer.extract_features(image_rgb)
            
            return {
                "features": features.tolist(),
                "feature_dim": len(features),
                "has_features": True,
                "shadow_mode": True,
                "model_type": "infrared"
            }
            
        except Exception as e:
            logger.error(f"Infrared feature extraction error: {str(e)}")
            return {
                "error": f"Failed to extract features: {str(e)}",
                "features": None,
                "feature_dim": 0,
                "has_features": False,
                "shadow_mode": True
            }
    
    def health_check(self) -> Dict:
        """健康检查"""
        return {
            "status": "healthy" if self.is_loaded else "unhealthy",
            "model_loaded": self.is_loaded,
            "device": self.device,
            "model_path": self.model_path,
            "reference_features_path": self.reference_features_path,
            "shadow_mode": True,
            "model_type": "infrared"
        }
