#!/usr/bin/env python3
"""
Triton Server启动脚本与API代理服务器
基于猫咪个体识别的API服务
"""

import os
import sys
import time
import signal
import subprocess
import threading
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import json
import base64

# Web框架
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Triton客户端
import tritonclient.http as httpclient
import numpy as np

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 鉴权配置
CABY_VISION_API_KEY = os.getenv("CABY_VISION_API_KEY", "default_api_key")
security = HTTPBearer()

# API模型
class PredictRequest(BaseModel):
    image: str  # base64编码的图像
    return_features: bool = False
    return_confidence: bool = True
    task: str = "predict"



class HealthResponse(BaseModel):
    status: str
    model_loaded: bool
    device: str
    classes: list
    num_classes: int

# 创建FastAPI应用
app = FastAPI(
    title="Caby Vision API (Triton Backend)",
    description="Caby Vision API using NVIDIA Triton Server",
    version="2.0.0"
)

# CORS配置
cors_origins = os.getenv("CORS_ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:8080").split(",")
app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Triton客户端
triton_client = None

def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证API密钥"""
    if credentials.credentials != CABY_VISION_API_KEY:
        raise HTTPException(status_code=401, detail="Invalid API key")
    return credentials.credentials

def get_triton_client():
    """获取Triton客户端"""
    global triton_client
    if triton_client is None:
        try:
            triton_client = httpclient.InferenceServerClient(url="localhost:8000")
            if not triton_client.is_server_ready():
                raise ConnectionError("Triton server is not ready")
        except Exception as e:
            logger.error(f"Failed to connect to Triton server: {e}")
            raise HTTPException(status_code=503, detail="Triton server unavailable")
    return triton_client

async def call_triton_model(image_data: str = "", 
                          return_features: bool = False,
                          return_confidence: bool = True,
                          task_type: str = "predict",
                          features1: list = None,
                          features2: list = None) -> Dict[str, Any]:
    """调用Triton模型"""
    client = get_triton_client()
    
    try:
        # 准备输入
        inputs = []
        outputs = []
        
        # 图像输入
        image_input = httpclient.InferInput("IMAGE", [1], "BYTES")
        image_input.set_data_from_numpy(np.array([image_data], dtype=object))
        inputs.append(image_input)
        
        # 可选参数
        return_features_input = httpclient.InferInput("RETURN_FEATURES", [1], "BOOL")
        return_features_input.set_data_from_numpy(np.array([return_features], dtype=bool))
        inputs.append(return_features_input)
        
        return_confidence_input = httpclient.InferInput("RETURN_CONFIDENCE", [1], "BOOL")
        return_confidence_input.set_data_from_numpy(np.array([return_confidence], dtype=bool))
        inputs.append(return_confidence_input)
        
        task_input = httpclient.InferInput("TASK_TYPE", [1], "BYTES")
        task_input.set_data_from_numpy(np.array([task_type], dtype=object))
        inputs.append(task_input)
        
        # 相似度计算的特征向量
        features1_str = json.dumps(features1) if features1 else ""
        features1_input = httpclient.InferInput("FEATURES1", [1], "BYTES")
        features1_input.set_data_from_numpy(np.array([features1_str], dtype=object))
        inputs.append(features1_input)
        
        features2_str = json.dumps(features2) if features2 else ""
        features2_input = httpclient.InferInput("FEATURES2", [1], "BYTES")
        features2_input.set_data_from_numpy(np.array([features2_str], dtype=object))
        inputs.append(features2_input)
        
        # 输出
        outputs.append(httpclient.InferRequestedOutput("SUCCESS"))
        outputs.append(httpclient.InferRequestedOutput("ERROR_MESSAGE"))
        outputs.append(httpclient.InferRequestedOutput("RESULTS"))
        
        # 执行推理
        response = client.infer("caby_vision", inputs, outputs=outputs)
        
        # 解析结果
        success = response.as_numpy("SUCCESS")[0]
        error_message = response.as_numpy("ERROR_MESSAGE")[0].decode('utf-8')
        results_json = response.as_numpy("RESULTS")[0].decode('utf-8')
        
        if success:
            results = json.loads(results_json)
            return {
                "success": True,
                "error": None,
                "results": results
            }
        else:
            return {
                "success": False,
                "error": error_message,
                "results": json.loads(results_json) if results_json else {}
            }
            
    except Exception as e:
        logger.error(f"Triton inference error: {e}")
        return {
            "success": False,
            "error": f"Inference failed: {str(e)}",
            "results": {}
        }

# API端点
@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        client = get_triton_client()
        if client.is_server_ready() and client.is_model_ready("caby_vision"):
            # 调用模型的health检查
            result = await call_triton_model("", task_type="health")
            if result["success"]:
                return result["results"]
            else:
                raise HTTPException(status_code=503, detail=result["error"])
        else:
            raise HTTPException(status_code=503, detail="Triton server or model not ready")
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Health check failed: {str(e)}")

@app.post("/predict")
async def predict_cat(request: PredictRequest, api_key: str = Depends(verify_api_key)):
    """猫咪个体识别端点"""
    result = await call_triton_model(
        image_data=request.image,
        return_features=request.return_features,
        return_confidence=request.return_confidence,
        task_type=request.task
    )
    
    if not result["success"]:
        raise HTTPException(status_code=400, detail=result["error"])
    
    return result





@app.get("/")
async def root():
    """根端点 - 显示可用的API端点"""
    return {
        "message": "Caby Vision API - 猫咪个体识别服务",
        "description": "基于MegaDescriptor的猫咪个体识别API服务",
        "version": "2.0.0",
        "endpoints": {
            "health": {
                "path": "/health",
                "method": "GET",
                "description": "服务健康检查"
            },
            "predict": {
                "path": "/predict",
                "method": "POST",
                "description": "猫咪个体识别预测"
            }
        },
        "documentation": "/docs"
    }

class TritonServerManager:
    """Triton服务器管理器"""
    
    def __init__(self):
        self.triton_process = None
        self.api_process = None
        
    def start_triton_server(self):
        """启动Triton服务器"""
        logger.info("Starting Triton Inference Server...")
        
        # 检查模型仓库是否存在
        model_repo_path = "/app/model_repository"
        if not os.path.exists(model_repo_path):
            raise RuntimeError(f"Model repository not found: {model_repo_path}")
        
        # 检查caby_vision模型配置
        caby_vision_model_path = f"{model_repo_path}/caby_vision"
        if not os.path.exists(caby_vision_model_path):
            raise RuntimeError(f"Caby Vision model not found: {caby_vision_model_path}")
        
        config_path = f"{caby_vision_model_path}/config.pbtxt"
        if not os.path.exists(config_path):
            raise RuntimeError(f"Model config not found: {config_path}")
        
        model_py_path = f"{caby_vision_model_path}/1/model.py"
        if not os.path.exists(model_py_path):
            raise RuntimeError(f"Model Python file not found: {model_py_path}")
        
        logger.info(f"Model repository: {model_repo_path}")
        logger.info(f"Caby Vision config: {config_path}")
        logger.info(f"Model Python backend: {model_py_path}")
        
        triton_cmd = [
            "tritonserver",
            "--model-repository=/app/model_repository",
            "--http-port=8000",
            "--allow-grpc=false",  # 禁用gRPC
            "--metrics-port=8002",
            "--log-verbose=1",
            "--log-info=true",
            "--log-warning=true",
            "--log-error=true",
            "--allow-http=true",
            "--allow-metrics=true"
        ]
        
        logger.info(f"Triton command: {' '.join(triton_cmd)}")
        
        try:
            # 不重定向输出，让Triton的日志直接显示
            self.triton_process = subprocess.Popen(
                triton_cmd,
                # stdout=None,  # 直接输出到控制台
                # stderr=None,  # 直接输出到控制台
                universal_newlines=True
            )
            
            # 等待Triton服务器启动
            logger.info("Waiting for Triton server to start...")
            time.sleep(15)  # 增加等待时间
            
            # 检查进程是否还在运行
            if self.triton_process.poll() is not None:
                exit_code = self.triton_process.returncode
                raise RuntimeError(f"Triton server exited with code {exit_code}")
            
            # 检查服务器状态
            logger.info("Checking Triton server status...")
            for i in range(30):  # 最多等待30秒
                try:
                    client = httpclient.InferenceServerClient(url="localhost:8000")
                    if client.is_server_ready():
                        logger.info("Triton server is ready!")
                        
                        # 检查模型状态
                        if client.is_model_ready("caby_vision"):
                            logger.info("Caby Vision model is ready!")
                        else:
                            logger.warning("Caby Vision model is not ready yet...")
                        break
                except Exception as check_e:
                    logger.debug(f"Health check attempt {i+1}/30 failed: {check_e}")
                    pass
                time.sleep(1)
            else:
                # 输出进程状态
                if self.triton_process.poll() is not None:
                    exit_code = self.triton_process.returncode
                    raise RuntimeError(f"Triton server exited with code {exit_code} during startup")
                else:
                    raise RuntimeError("Triton server failed to become ready within 30 seconds")
                
        except Exception as e:
            logger.error(f"Failed to start Triton server: {e}")
            
            # 如果进程还在运行，尝试获取一些输出信息
            if self.triton_process and self.triton_process.poll() is None:
                logger.info("Terminating Triton process...")
                self.triton_process.terminate()
                self.triton_process.wait()
            
            raise
    
    def start_api_server(self):
        """启动API代理服务器"""
        logger.info("Starting API proxy server...")
        
        host = os.getenv("CABY_VISION_HOST", "0.0.0.0")
        port = int(os.getenv("CABY_VISION_PORT", "8001"))
        
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level="info"
        )
    
    def cleanup(self):
        """清理资源"""
        logger.info("Cleaning up...")
        
        if self.triton_process:
            self.triton_process.terminate()
            self.triton_process.wait()
        
        if self.api_process:
            self.api_process.terminate()
            self.api_process.wait()

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"Received signal {signum}, shutting down...")
    manager.cleanup()
    sys.exit(0)

def main():
    """主函数"""
    global manager
    manager = TritonServerManager()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动Triton服务器
        manager.start_triton_server()
        
        # 启动API代理服务器
        manager.start_api_server()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Server error: {e}")
    finally:
        manager.cleanup()

if __name__ == "__main__":
    main() 