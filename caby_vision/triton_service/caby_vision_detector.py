#!/usr/bin/env python3
"""
Cat ReID Detection Module
基于MegaDescriptor的猫咪个体识别核心功能
"""

import torch
import torch.nn.functional as F
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import base64
import io
from PIL import Image
import logging
import json
import warnings

# 抑制警告
warnings.filterwarnings('ignore')

# 导入Reid模型 - 使用原始reid模块
import sys
import os

# 检测运行环境，选择正确的reid路径
# 优先使用本地reid目录，提高部署便利性
current_dir = Path(__file__).parent.parent
local_reid_path = current_dir / "reid" / "training"

if local_reid_path.exists():
    # 使用项目内的reid目录（推荐）
    reid_path = local_reid_path
elif os.path.exists("/app/reid/training"):
    # Docker容器内路径（向后兼容）
    reid_path = Path("/app/reid/training")
else:
    # 使用本地集成的reid模块
    reid_path = Path(__file__).parent / "reid" / "training"

sys.path.insert(0, str(reid_path))

try:
    # 尝试导入本地版本（推荐）
    from models.caby_vision_model import CabyVisionModel as CatReidModel
except ImportError:
    # 回退到原始版本（向后兼容）
    from models.cat_reid_model import CatReidModel
from sklearn.isotonic import IsotonicRegression

# 导入红外猫咪检测器
try:
    from infrared_cat_detector import InfraredCatDetector
except ImportError as e:
    logging.warning(f"Failed to import InfraredCatDetector: {e}")
    InfraredCatDetector = None
import pickle
import torch.nn.functional as F

logger = logging.getLogger(__name__)


class CalibratedModel(torch.nn.Module):
    """校准后的模型包装器"""

    def __init__(self, base_model, temperature=1.0, feature_scaler=None):
        super().__init__()
        self.base_model = base_model
        self.temperature = temperature

        if feature_scaler is not None:
            self.feature_scaler = torch.nn.Parameter(feature_scaler)
            self.use_feature_scaling = True
        else:
            self.use_feature_scaling = False

    def forward(self, x, return_features=False):
        # 获取基础特征和logits
        features, logits = self.base_model(x, return_features=True)

        # 可选的特征缩放
        if self.use_feature_scaling:
            features = features * self.feature_scaler
            features = F.normalize(features, p=2, dim=1)
            # 重新计算logits
            logits = self.base_model.classifier(features)

        # 温度缩放
        scaled_logits = logits / self.temperature

        if return_features:
            return features, scaled_logits
        else:
            return scaled_logits

class CabyVisionDetector:
    """Caby Vision Detection and Recognition Service"""
    
    def __init__(self, 
                 model_path: str,
                 device: str = "auto",
                 img_size: int = 224):
        """
        初始化Caby Vision检测器
        
        Args:
            model_path: 模型文件路径
            device: 设备类型 ("auto", "cpu", "cuda", "gpu")
            img_size: 输入图像大小
        """
        self.model_path = model_path
        self.img_size = img_size
        
        # 设备设置
        if device == "auto":
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        elif device in ["gpu", "cuda"]:
            if torch.cuda.is_available():
                self.device = torch.device("cuda")
            else:
                logger.warning("CUDA not available, falling back to CPU")
                self.device = torch.device("cpu")
        elif device == "cpu":
            self.device = torch.device("cpu")
        else:
            logger.warning(f"Unknown device '{device}', using auto detection")
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            
        logger.info(f"Using device: {self.device}")
        if self.device.type == "cuda":
            logger.info(f"CUDA version: {torch.version.cuda}")
            logger.info(f"Available GPUs: {torch.cuda.device_count()}")
        else:
            logger.info("Running in CPU mode")
        
        # 类别映射 - 必须与训练时的字母顺序一致
        self.class_names = ["小白", "小花", "小黑"]  # 按字母顺序排序
        self.num_classes = len(self.class_names)
        
        # 加载模型
        self.model = None
        self.calibrator = None
        self.transform = None

        # 设置HuggingFace缓存路径
        import os
        os.environ['HF_HOME'] = '/cache/huggingface'
        os.environ['TRANSFORMERS_CACHE'] = '/cache/huggingface'

        # 设置随机种子确保一致性
        torch.manual_seed(42)
        np.random.seed(42)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(42)
            torch.cuda.manual_seed_all(42)

        # 设置确定性计算
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

        # 立即加载模型
        self._load_model()

        # 初始化影子模式 - 红外猫咪识别器
        self.shadow_mode_enabled = True
        self.infrared_detector = None
        if InfraredCatDetector is not None:
            try:
                device_str = "cuda" if self.device.type == "cuda" else "cpu"
                self.infrared_detector = InfraredCatDetector(device=device_str)
                logger.info("Shadow mode (infrared) detector initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize shadow mode detector: {e}")
                self.shadow_mode_enabled = False
        else:
            logger.warning("InfraredCatDetector not available, shadow mode disabled")
            self.shadow_mode_enabled = False

    def _create_local_megadescriptor_model(self):
        """创建本地MegaDescriptor兼容模型"""
        import torch.nn as nn

        # 创建一个简化的MegaDescriptor兼容模型
        class LocalMegaDescriptor(nn.Module):
            def __init__(self, num_classes=3, feature_dim=768):
                super().__init__()
                # 使用与MegaDescriptor相同的patch embedding配置
                self.patch_embed = nn.Conv2d(3, 96, kernel_size=4, stride=4)
                self.pos_embed = nn.Parameter(torch.randn(1, 3136 + 1, 96))  # 224/4 = 56, 56*56 = 3136
                self.cls_token = nn.Parameter(torch.randn(1, 1, 96))

                # Transformer layers
                encoder_layer = nn.TransformerEncoderLayer(
                    d_model=96, nhead=8, dim_feedforward=384, dropout=0.1, batch_first=True
                )
                self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=12)

                # Feature projection to match expected dimension
                self.feature_proj = nn.Linear(96, feature_dim)
                self.norm = nn.LayerNorm(feature_dim)

                # Classifier
                self.classifier = nn.Linear(feature_dim, num_classes)

            def forward(self, x, return_features=False):
                B = x.shape[0]

                # Patch embedding
                x = self.patch_embed(x)  # [B, 96, 56, 56]
                x = x.flatten(2).transpose(1, 2)  # [B, 3136, 96]

                # Add class token
                cls_tokens = self.cls_token.expand(B, -1, -1)
                x = torch.cat([cls_tokens, x], dim=1)  # [B, 3137, 96]

                # Add position embedding
                x = x + self.pos_embed

                # Transformer
                x = self.transformer(x)

                # Extract class token and project features
                cls_output = x[:, 0]  # [B, 96]
                features = self.feature_proj(cls_output)  # [B, feature_dim]
                features = self.norm(features)

                # Classifier
                logits = self.classifier(features)

                if return_features:
                    return features, logits
                else:
                    return logits

            def extract_features(self, x):
                """提取特征向量"""
                features, _ = self.forward(x, return_features=True)
                return features

        return LocalMegaDescriptor(num_classes=self.num_classes, feature_dim=768)
        
    def _load_model(self):
        """加载Caby Vision模型"""
        if not Path(self.model_path).exists():
            raise FileNotFoundError(f"Model file not found: {self.model_path}")

        try:
            # 加载checkpoint
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)

            # 检查checkpoint中的权重来确定正确的架构
            if 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            else:
                state_dict = checkpoint

            # 通过检查patch_embed权重的形状来确定架构
            patch_embed_weight = None
            for key in state_dict.keys():
                if 'backbone.patch_embed.proj.weight' in key:
                    patch_embed_weight = state_dict[key]
                    break

            if patch_embed_weight is not None:
                weight_shape = patch_embed_weight.shape
                print(f"检测到权重形状: {weight_shape}")

                # MegaDescriptor: [96, 3, 4, 4], ViT: [768, 3, 16, 16]
                if weight_shape[0] == 96 and weight_shape[2:] == (4, 4):
                    # MegaDescriptor架构
                    try:
                        backbone_name = "hf-hub:BVRA/MegaDescriptor-T-224"
                        base_model = CatReidModel(
                            num_classes=self.num_classes,
                            backbone=backbone_name,
                            feature_dim=768,
                            dropout=0.1,
                            pretrained=False,
                            freeze_backbone=False
                        )
                        print(f"使用MegaDescriptor架构（基于权重检测）")
                    except Exception as e:
                        print(f"无法创建MegaDescriptor模型: {e}")
                        print("尝试创建本地兼容的MegaDescriptor模型...")
                        # 创建一个本地的兼容模型
                        base_model = self._create_local_megadescriptor_model()
                        print(f"使用本地MegaDescriptor兼容模型")
                elif weight_shape[0] == 768 and weight_shape[2:] == (16, 16):
                    # ViT架构
                    backbone_name = "vit_base_patch16_224"
                    base_model = CatReidModel(
                        num_classes=self.num_classes,
                        backbone=backbone_name,
                        feature_dim=768,
                        dropout=0.1,
                        pretrained=False,
                        freeze_backbone=False
                    )
                    print(f"使用ViT架构（基于权重检测）")
                else:
                    raise RuntimeError(f"未知的权重架构: {weight_shape}")
            else:
                # 如果找不到patch_embed权重，尝试默认架构
                try:
                    backbone_name = "hf-hub:BVRA/MegaDescriptor-T-224"
                    base_model = CatReidModel(
                        num_classes=self.num_classes,
                        backbone=backbone_name,
                        feature_dim=768,
                        dropout=0.1,
                        pretrained=False,
                        freeze_backbone=False
                    )
                    print(f"使用MegaDescriptor架构（默认）")
                except Exception as e:
                    print(f"无法创建MegaDescriptor模型: {e}")
                    backbone_name = "vit_base_patch16_224"
                    base_model = CatReidModel(
                        num_classes=self.num_classes,
                        backbone=backbone_name,
                        feature_dim=768,
                        dropout=0.1,
                        pretrained=False,
                        freeze_backbone=False
                    )
                    print(f"回退到ViT架构")

            # 加载基础模型权重
            if 'model_state_dict' in checkpoint:
                base_model.load_state_dict(checkpoint['model_state_dict'], strict=False)
            else:
                base_model.load_state_dict(checkpoint, strict=False)

            # 检查是否为校准模型
            if 'temperature' in checkpoint:
                print("检测到校准模型")
                temperature = checkpoint['temperature']
                feature_scaler = checkpoint.get('feature_scaler', None)

                # 创建校准后的模型
                self.model = CalibratedModel(
                    base_model,
                    temperature=temperature,
                    feature_scaler=feature_scaler
                )

                # 打印校准信息
                if 'calibration_results' in checkpoint:
                    results = checkpoint['calibration_results']
                    print(f"校准结果:")
                    print(f"  - 温度参数: {temperature}")
                    print(f"  - 准确率: {results['accuracy']:.4f}")
            else:
                self.model = base_model
                print("使用普通模型")

            # 加载校准器（如果存在）
            if 'calibrator_state' in checkpoint:
                calibrator_state = checkpoint['calibrator_state']
                self.calibrator = IsotonicRegression(out_of_bounds='clip')
                self.calibrator.X_thresholds_ = calibrator_state['X_thresholds_']
                self.calibrator.y_thresholds_ = calibrator_state['y_thresholds_']
                self.calibrator.increasing_ = calibrator_state['increasing_']
                print("加载置信度校准器")

            self.model.to(self.device)
            self.model.eval()

            print(f"模型加载成功: {self.model_path}")
            return True

        except Exception as e:
            print(f"Failed to load model: {e}")
            return False
    
    def _setup_transform(self):
        """设置图像预处理"""
        from torchvision import transforms
        
        self.transform = transforms.Compose([
            transforms.Resize((self.img_size, self.img_size)),
            transforms.ToTensor(),
            transforms.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            )
        ])
        logger.info("Transform setup completed")
    
    def _preprocess_image(self, image: np.ndarray) -> torch.Tensor:
        """预处理图像"""
        # 转换为PIL图像
        # 注意：从base64解码的图像已经是RGB格式，不需要BGR2RGB转换
        pil_image = Image.fromarray(image)

        # 确保是RGB模式
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')

        # 应用变换
        tensor_image = self.transform(pil_image)
        tensor_image = tensor_image.unsqueeze(0).to(self.device)

        return tensor_image
    
    def predict_image(self, 
                     image: np.ndarray, 
                     return_features: bool = False,
                     return_confidence: bool = True) -> Dict:
        """
        预测图像中的猫咪个体
        
        Args:
            image: 输入图像 (BGR格式)
            return_features: 是否返回特征向量
            return_confidence: 是否返回置信度
            
        Returns:
            包含预测结果的字典
        """
        if self.model is None:
            return {
                "error": "Model not loaded",
                "predicted_cat": None,
                "confidence": 0.0,
                "features": None
            }
        
        if self.transform is None:
            self._setup_transform()
        
        try:
            # 预处理
            img_tensor = self._preprocess_image(image)
            
            # 推理
            with torch.no_grad():
                if return_features:
                    features, logits = self.model(img_tensor, return_features=True)
                else:
                    logits = self.model(img_tensor)
                    features = None
                
                # 计算概率
                probabilities = F.softmax(logits, dim=1)
                # 使用与训练脚本相同的方法计算置信度，确保一致性
                predicted_idx = torch.argmax(logits, dim=1).item()
                confidence = probabilities[0, predicted_idx].item()

                predicted_cat = self.class_names[predicted_idx]

                # 调试输出（可选）
                # print(f"API logits: {logits[0].tolist()}")
                # print(f"API原始置信度: {confidence:.11f}")

                # 应用置信度校准
                if self.calibrator is not None and return_confidence:
                    confidence = self.calibrator.predict([confidence])[0]
                    confidence = float(np.clip(confidence, 0.0, 1.0))
                    # 确保高精度输出用于调试
                    print(f"API校准后置信度: {confidence:.11f}")
            
            result = {
                "predicted_cat": predicted_cat,
                "confidence": confidence,
                "class_probabilities": {
                    name: float(prob) for name, prob in
                    zip(self.class_names, probabilities[0].cpu().numpy())
                }
            }

            if return_features and features is not None:
                result["features"] = features.cpu().numpy().tolist()

            # 影子模式 - 同时运行红外猫咪识别
            if self.shadow_mode_enabled and self.infrared_detector is not None:
                try:
                    shadow_result = self.infrared_detector.predict_image(
                        image, return_features, return_confidence
                    )
                    result["shadow_result"] = shadow_result
                    logger.info(f"Shadow mode result: {shadow_result.get('predicted_cat')} "
                              f"(confidence: {shadow_result.get('confidence', 0.0):.4f})")
                except Exception as e:
                    logger.error(f"Shadow mode prediction failed: {e}")
                    result["shadow_result"] = {"error": str(e), "shadow_mode": True}

            return result
            
        except Exception as e:
            logger.error(f"Prediction error: {str(e)}")
            return {
                "error": f"Prediction failed: {str(e)}",
                "predicted_cat": None,
                "confidence": 0.0,
                "features": None
            }
    
    def predict_from_base64(self, 
                           image_base64: str, 
                           return_features: bool = False,
                           return_confidence: bool = True) -> Dict:
        """
        从base64编码的图像进行预测
        
        Args:
            image_base64: base64编码的图像
            return_features: 是否返回特征向量
            return_confidence: 是否返回置信度
            
        Returns:
            包含预测结果的字典
        """
        try:
            # 解码base64图像
            image_bytes = base64.b64decode(image_base64)
            image_pil = Image.open(io.BytesIO(image_bytes))
            image_np = np.array(image_pil)
            
            # 调用预测函数
            return self.predict_image(image_np, return_features, return_confidence)
            
        except Exception as e:
            logger.error(f"Base64 prediction error: {str(e)}")
            return {
                "error": f"Failed to process base64 image: {str(e)}",
                "predicted_cat": None,
                "confidence": 0.0,
                "features": None
            }
    
    def get_cat_features(self, image: np.ndarray) -> Dict:
        """
        提取猫咪特征向量
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            包含特征向量的字典
        """
        if self.model is None:
            return {
                "error": "Model not loaded",
                "features": None,
                "feature_dim": 0
            }
        
        if self.transform is None:
            self._setup_transform()
        
        try:
            # 预处理
            img_tensor = self._preprocess_image(image)
            
            # 提取特征
            with torch.no_grad():
                features = self.model.extract_features(img_tensor)
                features_np = features.cpu().numpy()
            
            return {
                "features": features_np.tolist(),
                "feature_dim": features_np.shape[1],
                "has_features": True
            }
            
        except Exception as e:
            logger.error(f"Feature extraction error: {str(e)}")
            return {
                "error": f"Failed to extract features: {str(e)}",
                "features": None,
                "feature_dim": 0,
                "has_features": False
            }

    def predict_infrared(self,
                        image_base64: str,
                        return_features: bool = False,
                        return_confidence: bool = True) -> Dict:
        """
        专门的红外猫咪识别端点

        Args:
            image_base64: base64编码的图像
            return_features: 是否返回特征向量
            return_confidence: 是否返回置信度

        Returns:
            包含红外识别结果的字典
        """
        if not self.shadow_mode_enabled or self.infrared_detector is None:
            return {
                "error": "Infrared detection not available",
                "predicted_cat": None,
                "confidence": 0.0,
                "features": None,
                "shadow_mode": True
            }

        try:
            result = self.infrared_detector.predict_from_base64(
                image_base64, return_features, return_confidence
            )
            return result

        except Exception as e:
            logger.error(f"Infrared prediction error: {str(e)}")
            return {
                "error": f"Infrared prediction failed: {str(e)}",
                "predicted_cat": None,
                "confidence": 0.0,
                "features": None,
                "shadow_mode": True
            }

    def get_infrared_features(self, image_base64: str) -> Dict:
        """
        提取红外图像特征向量

        Args:
            image_base64: base64编码的图像

        Returns:
            包含特征向量的字典
        """
        if not self.shadow_mode_enabled or self.infrared_detector is None:
            return {
                "error": "Infrared detection not available",
                "features": None,
                "feature_dim": 0,
                "shadow_mode": True
            }

        try:
            # 解码base64图像
            image_bytes = base64.b64decode(image_base64)
            image_pil = Image.open(io.BytesIO(image_bytes))
            image_np = np.array(image_pil)

            result = self.infrared_detector.get_cat_features(image_np)
            return result

        except Exception as e:
            logger.error(f"Infrared feature extraction error: {str(e)}")
            return {
                "error": f"Infrared feature extraction failed: {str(e)}",
                "features": None,
                "feature_dim": 0,
                "shadow_mode": True
            }

    def get_cat_features_from_base64(self, image_base64: str) -> Dict:
        """
        从base64字符串提取猫咪特征向量（主模型）

        Args:
            image_base64: base64编码的图像

        Returns:
            包含特征向量的字典
        """
        try:
            # 解码base64图像
            image_bytes = base64.b64decode(image_base64)
            image_pil = Image.open(io.BytesIO(image_bytes))
            image_np = np.array(image_pil)

            # 调用特征提取函数
            return self.get_cat_features(image_np)

        except Exception as e:
            logger.error(f"Base64 feature extraction error: {str(e)}")
            return {
                "error": f"Failed to extract features from base64 image: {str(e)}",
                "features": None,
                "feature_dim": 0,
                "has_features": False
            }
    
    def compute_similarity(self, features1: np.ndarray, features2: np.ndarray) -> float:
        """
        计算两个特征向量的余弦相似度
        
        Args:
            features1: 第一个特征向量
            features2: 第二个特征向量
            
        Returns:
            余弦相似度 (-1 到 1)
        """
        try:
            # 确保是numpy数组
            if isinstance(features1, list):
                features1 = np.array(features1)
            if isinstance(features2, list):
                features2 = np.array(features2)
            
            # L2归一化
            features1 = features1 / np.linalg.norm(features1)
            features2 = features2 / np.linalg.norm(features2)
            
            # 计算余弦相似度
            similarity = np.dot(features1, features2)
            
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Similarity computation error: {str(e)}")
            return 0.0 