#!/usr/bin/env python3
"""
红外猫咪识别配置管理
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class InfraredConfig:
    """红外模型配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认路径
        """
        self.config_path = config_path or self._get_default_config_path()
        self.config = self._load_config()
        self._apply_env_overrides()
    
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        current_dir = os.path.dirname(__file__)
        return os.path.join(current_dir, '..', 'config', 'infrared_config.yaml')
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                logger.info(f"Loaded infrared config from: {self.config_path}")
                return config
            else:
                logger.warning(f"Config file not found: {self.config_path}, using defaults")
                return self._get_default_config()
        except Exception as e:
            logger.error(f"Failed to load config: {e}, using defaults")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'infrared_model': {
                'model_path': 'models/infrared/infrared_cat_model_quantized.onnx',
                'reference_features_path': 'models/infrared/reference_features.json',
                'device': 'auto',
                'input_size': 224,
                'feature_dim': 512,
                'k_neighbors': 7,
                'distance_metric': 'cosine',
                'batch_size': 1,
                'num_workers': 1
            }
        }
    
    def _apply_env_overrides(self):
        """应用环境变量覆盖"""
        env_overrides = {
            'INFRARED_MODEL_PATH': ['infrared_model', 'model_path'],
            'INFRARED_FEATURES_PATH': ['infrared_model', 'reference_features_path'],
            'INFRARED_DEVICE': ['infrared_model', 'device']
        }
        
        for env_var, config_path in env_overrides.items():
            env_value = os.getenv(env_var)
            if env_value:
                self._set_nested_config(config_path, env_value)
                logger.info(f"Applied env override: {env_var}={env_value}")
    
    def _set_nested_config(self, path: list, value: str):
        """设置嵌套配置值"""
        current = self.config
        for key in path[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[path[-1]] = value
    
    def get_model_path(self) -> str:
        """获取模型文件的绝对路径"""
        model_path = self.config['infrared_model']['model_path']
        if os.path.isabs(model_path):
            return model_path
        else:
            # 相对于 caby_vision 根目录
            caby_vision_root = os.path.dirname(os.path.dirname(__file__))
            return os.path.join(caby_vision_root, model_path)
    
    def get_reference_features_path(self) -> str:
        """获取参考特征文件的绝对路径"""
        features_path = self.config['infrared_model']['reference_features_path']
        if os.path.isabs(features_path):
            return features_path
        else:
            # 相对于 caby_vision 根目录
            caby_vision_root = os.path.dirname(os.path.dirname(__file__))
            return os.path.join(caby_vision_root, features_path)
    
    def get_device(self) -> str:
        """获取设备配置"""
        return self.config['infrared_model']['device']
    
    def get_input_size(self) -> int:
        """获取输入图像大小"""
        return self.config['infrared_model']['input_size']
    
    def get_feature_dim(self) -> int:
        """获取特征维度"""
        return self.config['infrared_model']['feature_dim']
    
    def get_k_neighbors(self) -> int:
        """获取KNN的k值"""
        return self.config['infrared_model']['k_neighbors']
    
    def get_distance_metric(self) -> str:
        """获取距离度量"""
        return self.config['infrared_model']['distance_metric']
    
    def validate_paths(self) -> bool:
        """验证模型文件路径是否存在"""
        model_path = self.get_model_path()
        features_path = self.get_reference_features_path()
        
        if not os.path.exists(model_path):
            logger.error(f"Model file not found: {model_path}")
            return False
        
        if not os.path.exists(features_path):
            logger.error(f"Reference features file not found: {features_path}")
            return False
        
        logger.info(f"Model files validated successfully:")
        logger.info(f"  Model: {model_path}")
        logger.info(f"  Features: {features_path}")
        return True
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'model_path': self.get_model_path(),
            'reference_features_path': self.get_reference_features_path(),
            'device': self.get_device(),
            'input_size': self.get_input_size(),
            'feature_dim': self.get_feature_dim(),
            'k_neighbors': self.get_k_neighbors(),
            'distance_metric': self.get_distance_metric(),
            'paths_valid': self.validate_paths()
        }

# 全局配置实例
_infrared_config = None

def get_infrared_config() -> InfraredConfig:
    """获取全局红外配置实例"""
    global _infrared_config
    if _infrared_config is None:
        _infrared_config = InfraredConfig()
    return _infrared_config

def reload_infrared_config(config_path: Optional[str] = None):
    """重新加载配置"""
    global _infrared_config
    _infrared_config = InfraredConfig(config_path)
    return _infrared_config
