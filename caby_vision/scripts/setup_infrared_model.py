#!/usr/bin/env python3
"""
红外猫咪识别模型部署脚本
用于独立部署，不依赖 reid 目录
"""

import os
import sys
import shutil
import requests
import argparse
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_directories():
    """创建必要的目录结构"""
    base_dir = Path(__file__).parent.parent
    
    directories = [
        base_dir / "models" / "infrared",
        base_dir / "config",
        base_dir / "logs"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {directory}")
    
    return base_dir

def download_model_files(base_dir: Path, source_url: str = None):
    """下载或复制模型文件"""
    models_dir = base_dir / "models" / "infrared"
    
    # 如果提供了源URL，从网络下载
    if source_url:
        logger.info(f"Downloading model files from: {source_url}")
        # TODO: 实现从网络下载模型文件的逻辑
        # 这里可以从云存储、模型仓库等下载
        pass
    else:
        # 从本地复制（如果存在）
        logger.info("Looking for local model files...")
        
        # 尝试从多个可能的位置查找模型文件
        possible_sources = [
            Path("reid/infrared_cat_recognition_project/deployment"),
            Path("../reid/infrared_cat_recognition_project/deployment"),
            Path("../../reid/infrared_cat_recognition_project/deployment"),
        ]
        
        source_dir = None
        for possible_source in possible_sources:
            if possible_source.exists():
                source_dir = possible_source
                break
        
        if source_dir:
            logger.info(f"Found source directory: {source_dir}")
            
            # 复制模型文件
            files_to_copy = [
                "infrared_cat_model_quantized.onnx",
                "reference_features.json",
                "infrared_cat_recognizer.py"
            ]
            
            for file_name in files_to_copy:
                source_file = source_dir / file_name
                target_file = models_dir / file_name
                
                if source_file.exists():
                    shutil.copy2(source_file, target_file)
                    logger.info(f"Copied: {file_name}")
                else:
                    logger.warning(f"Source file not found: {source_file}")
        else:
            logger.error("No source directory found. Please provide model files manually.")
            return False
    
    return True

def create_default_config(base_dir: Path):
    """创建默认配置文件"""
    config_file = base_dir / "config" / "infrared_config.yaml"
    
    if config_file.exists():
        logger.info(f"Config file already exists: {config_file}")
        return
    
    config_content = """# 红外猫咪识别模型配置
infrared_model:
  # 模型文件路径（相对于 caby_vision 根目录）
  model_path: "models/infrared/infrared_cat_model_quantized.onnx"
  
  # 参考特征文件路径
  reference_features_path: "models/infrared/reference_features.json"
  
  # 设备配置
  device: "auto"  # auto, cpu, cuda
  
  # 模型参数
  input_size: 224
  feature_dim: 512
  
  # KNN 参数
  k_neighbors: 7
  distance_metric: "cosine"
  
  # 性能配置
  batch_size: 1
  num_workers: 1

# 环境变量覆盖
# 可以通过以下环境变量覆盖配置：
# INFRARED_MODEL_PATH - 模型文件路径
# INFRARED_FEATURES_PATH - 参考特征文件路径
# INFRARED_DEVICE - 运行设备
"""
    
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    logger.info(f"Created config file: {config_file}")

def install_dependencies():
    """安装必要的依赖"""
    logger.info("Installing Python dependencies...")
    
    dependencies = [
        "onnxruntime",
        "scikit-learn",
        "pillow",
        "numpy",
        "pyyaml"
    ]
    
    for dep in dependencies:
        try:
            __import__(dep.replace('-', '_'))
            logger.info(f"✓ {dep} already installed")
        except ImportError:
            logger.info(f"Installing {dep}...")
            os.system(f"pip install {dep}")

def verify_installation(base_dir: Path):
    """验证安装"""
    logger.info("Verifying installation...")
    
    # 检查文件是否存在
    required_files = [
        base_dir / "models" / "infrared" / "infrared_cat_model_quantized.onnx",
        base_dir / "models" / "infrared" / "reference_features.json",
        base_dir / "models" / "infrared" / "infrared_cat_recognizer.py",
        base_dir / "config" / "infrared_config.yaml"
    ]
    
    all_files_exist = True
    for file_path in required_files:
        if file_path.exists():
            logger.info(f"✓ {file_path.name}")
        else:
            logger.error(f"✗ {file_path.name} not found")
            all_files_exist = False
    
    if all_files_exist:
        logger.info("✅ All required files are present")
        
        # 尝试导入和初始化
        try:
            sys.path.insert(0, str(base_dir / "triton_service"))
            from infrared_cat_detector import InfraredCatDetector
            
            detector = InfraredCatDetector()
            health = detector.health_check()
            
            if health.get("model_loaded"):
                logger.info("✅ Model loaded successfully")
                return True
            else:
                logger.error("✗ Model failed to load")
                return False
                
        except Exception as e:
            logger.error(f"✗ Failed to initialize detector: {e}")
            return False
    else:
        logger.error("❌ Installation verification failed")
        return False

def main():
    parser = argparse.ArgumentParser(description="Setup infrared cat recognition model")
    parser.add_argument("--source-url", help="URL to download model files from")
    parser.add_argument("--skip-deps", action="store_true", help="Skip dependency installation")
    parser.add_argument("--verify-only", action="store_true", help="Only verify existing installation")
    
    args = parser.parse_args()
    
    logger.info("Starting infrared model setup...")
    
    # 设置目录
    base_dir = setup_directories()
    
    if not args.verify_only:
        # 安装依赖
        if not args.skip_deps:
            install_dependencies()
        
        # 下载/复制模型文件
        if not download_model_files(base_dir, args.source_url):
            logger.error("Failed to setup model files")
            sys.exit(1)
        
        # 创建配置文件
        create_default_config(base_dir)
    
    # 验证安装
    if verify_installation(base_dir):
        logger.info("🎉 Infrared model setup completed successfully!")
        logger.info(f"Model directory: {base_dir / 'models' / 'infrared'}")
        logger.info(f"Config file: {base_dir / 'config' / 'infrared_config.yaml'}")
        logger.info("\nNext steps:")
        logger.info("1. Start the caby_vision service")
        logger.info("2. Test the infrared endpoints")
        logger.info("3. Configure caby_ai to use the new endpoints")
    else:
        logger.error("❌ Setup failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
