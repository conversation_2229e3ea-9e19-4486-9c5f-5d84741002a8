#!/bin/bash

# Caby Vision 快速部署脚本 - 支持GPU和CPU模式
# 用于快速部署猫咪个体识别服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查GPU可用性
check_gpu_availability() {
    log_info "检查GPU可用性..."
    
    # 检查 nvidia-smi
    if command -v nvidia-smi &> /dev/null; then
        if nvidia-smi &> /dev/null; then
            log_success "检测到NVIDIA GPU"
            GPU_AVAILABLE=true
            nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits | while read line; do
                log_info "GPU: $line"
            done
        else
            log_warning "nvidia-smi 命令存在但无法访问GPU"
            GPU_AVAILABLE=false
        fi
    else
        log_warning "未检测到NVIDIA GPU或nvidia-smi命令"
        GPU_AVAILABLE=false
    fi
    
    # 检查 Docker GPU 支持
    if [ "$GPU_AVAILABLE" = true ]; then
        if nvidia-smi &> /dev/null; then
            log_success "Docker GPU支持可用"
            DOCKER_GPU_AVAILABLE=true
        else
            log_warning "Docker GPU支持不可用，将使用CPU模式"
            DOCKER_GPU_AVAILABLE=false
        fi
    else
        DOCKER_GPU_AVAILABLE=false
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 检查模型文件
check_models() {
    log_info "检查模型文件..."
    
    MODEL_PATH="models/caby_vision/best_model_calibrated.pth"
    BACKUP_MODEL_PATH="models/caby_vision/best_model.pth"
    
    if [ -f "$MODEL_PATH" ]; then
        log_success "找到校准模型: $MODEL_PATH"
        MODEL_SIZE=$(du -h "$MODEL_PATH" | cut -f1)
        log_info "模型大小: $MODEL_SIZE"
    elif [ -f "$BACKUP_MODEL_PATH" ]; then
        log_warning "未找到校准模型，使用原始模型: $BACKUP_MODEL_PATH"
        MODEL_SIZE=$(du -h "$BACKUP_MODEL_PATH" | cut -f1)
        log_info "模型大小: $MODEL_SIZE"
    else
        log_error "未找到任何模型文件，请检查模型路径"
        log_error "期望路径: $MODEL_PATH 或 $BACKUP_MODEL_PATH"
        exit 1
    fi
    
    # 检查 Triton 模型配置
    TRITON_CONFIG="triton_service/model_repository/caby_vision/config.pbtxt"
    if [ -f "$TRITON_CONFIG" ]; then
        log_success "找到 Triton 模型配置: $TRITON_CONFIG"
    else
        log_error "未找到 Triton 模型配置: $TRITON_CONFIG"
        exit 1
    fi
    
    TRITON_MODEL_PY="triton_service/model_repository/caby_vision/1/model.py"
    if [ -f "$TRITON_MODEL_PY" ]; then
        log_success "找到 Triton 模型实现: $TRITON_MODEL_PY"
    else
        log_error "未找到 Triton 模型实现: $TRITON_MODEL_PY"
        exit 1
    fi
}

# 选择部署模式
select_deployment_mode() {
    if [ "$DEVICE_MODE" != "auto" ]; then
        log_info "使用指定的设备模式: $DEVICE_MODE"
        return
    fi
    
    log_info "选择部署模式..."
    
    # 自动选择模式
    if [ "$DOCKER_GPU_AVAILABLE" = true ]; then
        DEVICE_MODE="gpu"
        log_success "自动选择GPU模式"
    else
        DEVICE_MODE="cpu"
        log_success "自动选择CPU模式"
    fi
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量 (${DEVICE_MODE}模式)..."

    # 使用统一的 Docker Compose 文件
    COMPOSE_FILE="docker-compose.yml"

    # 设置设备模式和服务配置
    if [ "$DEVICE_MODE" = "cpu" ]; then
        export CABY_VISION_DEVICE=cpu
        export COMPOSE_PROFILES=cpu
        export CUDA_VISIBLE_DEVICES=""
        SERVICE_NAME="caby_vision_cpu"
    else
        export CABY_VISION_DEVICE=auto
        export COMPOSE_PROFILES=gpu
        export NVIDIA_VISIBLE_DEVICES=all
        export NVIDIA_DRIVER_CAPABILITIES=compute,utility
        SERVICE_NAME="caby_vision"
    fi

    # 检查 compose 文件是否存在
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "Docker Compose 文件不存在: $COMPOSE_FILE"
        exit 1
    fi

    log_info "使用 Docker Compose 文件: $COMPOSE_FILE"
    log_info "使用服务配置: $SERVICE_NAME (profile: $COMPOSE_PROFILES)"

    # 设置 Triton 模型配置文件
    TRITON_CONFIG_DIR="triton_service/model_repository/caby_vision"
    if [ "$DEVICE_MODE" = "cpu" ]; then
        log_info "切换到CPU模式的Triton配置..."
        if [ -f "$TRITON_CONFIG_DIR/config.cpu.pbtxt" ]; then
            cp "$TRITON_CONFIG_DIR/config.cpu.pbtxt" "$TRITON_CONFIG_DIR/config.pbtxt"
            log_success "已切换到CPU配置文件"
        else
            log_error "CPU配置文件不存在: $TRITON_CONFIG_DIR/config.cpu.pbtxt"
            exit 1
        fi
    else
        log_info "使用GPU模式的Triton配置..."
        if [ -f "$TRITON_CONFIG_DIR/config.gpu.pbtxt" ]; then
            cp "$TRITON_CONFIG_DIR/config.gpu.pbtxt" "$TRITON_CONFIG_DIR/config.pbtxt"
            log_success "已切换到GPU配置文件"
        else
            log_info "使用默认GPU配置文件"
        fi
    fi

    # 创建 .env 文件（如果不存在）
    if [ ! -f ".env" ]; then
        log_info "创建 .env 文件..."
        cat > .env << EOF
# Caby Vision API 配置
CABY_VISION_API_KEY=caby_vision_api_key_$(date +%s)
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
CABY_VISION_MODEL_PATH=/app/models/caby_vision/best_model_calibrated.pth
CABY_VISION_DEVICE=$CABY_VISION_DEVICE

# GPU 配置 (仅在GPU模式下使用)
NVIDIA_VISIBLE_DEVICES=all
NVIDIA_DRIVER_CAPABILITIES=compute,utility

# 日志配置
LOG_LEVEL=INFO
EOF
        log_success "已创建 .env 文件"
    else
        log_success ".env 文件已存在"
        # 更新设备配置
        if grep -q "CABY_VISION_DEVICE=" .env; then
            sed -i "s/CABY_VISION_DEVICE=.*/CABY_VISION_DEVICE=$CABY_VISION_DEVICE/" .env
        else
            echo "CABY_VISION_DEVICE=$CABY_VISION_DEVICE" >> .env
        fi
    fi
    
    # 显示重要环境变量
    source .env
    log_info "部署模式: $DEVICE_MODE"
    log_info "设备配置: $CABY_VISION_DEVICE"
    log_info "API Key: $CABY_VISION_API_KEY"
    log_info "CORS Origins: $CORS_ALLOWED_ORIGINS"
}

# 构建 Docker 镜像
build_images() {
    if [ "$SKIP_BUILD" = true ]; then
        log_info "跳过镜像构建步骤"
        return
    fi
    
    log_info "构建 Docker 镜像 (${DEVICE_MODE}模式)..."
    
    # 检查是否需要重新构建
    local image_name="caby_vision"
    if [ "$DEVICE_MODE" = "cpu" ]; then
        image_name="caby_vision_cpu"
    fi
    
    if [ "$SKIP_BUILD" = false ]; then
        log_info "开始构建镜像 (这可能需要几分钟)..."

        docker-compose -f $COMPOSE_FILE build $SERVICE_NAME
        
        if [ $? -ne 0 ]; then
            log_error "镜像构建失败"
            exit 1
        fi
        
        log_success "镜像构建完成"
        
        # 清理无用的镜像
        log_info "清理无用的镜像 (<none>:<none>)..."
        dangling_images=$(docker images -f "dangling=true" -q)
        if [ ! -z "$dangling_images" ]; then
            docker image prune -f
            if [ $? -ne 0 ]; then
                log_warning "清理部分无用镜像失败"
            else
                log_success "成功清理无用镜像"
            fi
        else
            log_success "没有发现无用镜像"
        fi
    else
        log_info "跳过构建步骤 (使用了 --skip-build 参数)"
    fi
}

# 停止现有容器
stop_containers() {
    log_info "停止现有容器..."

    # 停止所有可能的容器 (使用统一的compose文件，但停止所有profiles)
    COMPOSE_PROFILES=gpu docker-compose -f docker-compose.yml down 2>/dev/null || true
    COMPOSE_PROFILES=cpu docker-compose -f docker-compose.yml down 2>/dev/null || true

    # 为了兼容性，也尝试停止旧的compose文件 (如果存在)
    docker-compose -f docker-compose.cpu.yml down 2>/dev/null || true

    log_success "成功停止容器"
}

# 启动服务
start_services() {
    log_info "启动 Caby Vision 服务 (${DEVICE_MODE}模式)..."
    
    # 启动服务
    docker-compose -f $COMPOSE_FILE up -d $SERVICE_NAME
    if [ $? -ne 0 ]; then
        log_error "服务启动失败"
        log_info "查看错误日志:"
        docker-compose -f $COMPOSE_FILE logs --tail=20 $SERVICE_NAME
        exit 1
    fi
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待 API 服务
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s http://localhost:8001/health > /dev/null 2>&1; then
            log_success "API 服务已就绪！"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "服务启动超时 (${max_attempts} 次尝试)"
            log_info "查看服务日志:"
            docker-compose -f $COMPOSE_FILE logs --tail=20
            exit 1
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    echo  # 换行
}

# 运行健康检查
health_check() {
    log_info "运行健康检查..."
    
    # 检查 API 健康状态
    local health_response=$(curl -s http://localhost:8001/health 2>/dev/null)
    if [ $? -eq 0 ]; then
        local status=$(echo "$health_response" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        local model_loaded=$(echo "$health_response" | grep -o '"model_loaded":[^,}]*' | cut -d':' -f2)
        local device=$(echo "$health_response" | grep -o '"device":"[^"]*"' | cut -d'"' -f4)
        
        if [ "$status" = "ok" ] && [ "$model_loaded" = "true" ]; then
            log_success "API 健康检查通过"
            log_info "运行设备: $device"
        else
            log_warning "API 健康检查异常 - Status: $status, Model: $model_loaded"
        fi
    else
        log_warning "API 健康检查失败"
    fi
    
    # 检查 Triton 服务器
    if curl -f -s http://localhost:8000/v2/health/ready > /dev/null 2>&1; then
        log_success "Triton 服务器健康检查通过"
    else
        log_warning "Triton 服务器健康检查失败"
    fi
    
    # 如果测试脚本存在，运行更详细的测试
    if [ -f "scripts/test.py" ]; then
        log_info "运行综合健康测试..."
        if python3 scripts/test.py health --verbose 2>/dev/null; then
            log_success "综合健康测试通过"
        else
            log_warning "综合健康测试失败，请查看详细日志"
        fi
    fi
}

# 显示容器状态
show_container_status() {
    log_info "容器状态:"
    docker-compose -f $COMPOSE_FILE ps
    
    echo
    log_info "容器详细信息:"
    
    # 获取运行中的容器信息
    container_id=$(docker-compose -f $COMPOSE_FILE ps -q $SERVICE_NAME 2>/dev/null)
    if [ ! -z "$container_id" ]; then
        echo -e "  ${SERVICE_NAME}: ${container_id}"
        echo -e "    查看日志: ${GREEN}docker logs ${container_id}${NC}"
    else
        echo -e "  ${SERVICE_NAME}: ${RED}未运行${NC}"
    fi
}

# 显示服务信息
show_service_info() {
    echo
    log_success "🐱 Caby Vision 服务部署完成！"
    echo
    echo "📋 服务信息:"
    echo "  运行模式: ${DEVICE_MODE^^}"
    echo "  API URL: http://localhost:8001"
    echo "  Triton Server: http://localhost:8000"
    echo "  API 文档: http://localhost:8001/docs"
    echo "  健康检查: http://localhost:8001/health"
    echo
    echo "🔑 认证信息:"
    if [ -f ".env" ]; then
        source .env 2>/dev/null || true
        echo "  API Key: ${CABY_VISION_API_KEY:-default_api_key}"
    else
        echo "  API Key: default_api_key"
    fi
    echo
    echo "🚀 快速测试:"
    echo "  curl http://localhost:8001/health"
    echo "  python3 scripts/test.py"
    echo
    echo "📊 查看日志:"
    echo "  docker-compose -f $COMPOSE_FILE logs -f"
    echo
    echo "🛑 停止服务:"
    echo "  docker-compose -f $COMPOSE_FILE down"
    echo
    if [ "$DEVICE_MODE" = "cpu" ]; then
        echo "💡 提示: 当前运行在CPU模式，如需GPU加速请安装NVIDIA Docker支持"
    else
        echo "💡 提示: 当前运行在GPU模式，使用硬件加速"
    fi
}

# 显示帮助信息
show_help() {
    echo "🐱 Caby Vision 服务快速部署脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  --device MODE      指定设备模式 (auto|gpu|cpu)"
    echo "                     auto: 自动检测 (默认)"
    echo "                     gpu:  强制使用GPU模式"
    echo "                     cpu:  强制使用CPU模式"
    echo "  --skip-build       跳过镜像构建步骤"
    echo "  --help, -h         显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0                    # 构建并部署 (自动检测GPU/CPU)"
    echo "  $0 --device cpu       # 构建并部署 (强制使用CPU模式)"
    echo "  $0 --device gpu       # 构建并部署 (强制使用GPU模式)"
    echo "  $0 --skip-build       # 跳过构建，只重新部署"
}

# 主函数
main() {
    echo "🐱 Caby Vision 服务快速部署脚本"
    echo "=================================="
    echo
    
    # 检查当前目录
    if [ ! -f "docker-compose.yml" ]; then
        log_error "请在 caby_vision 目录下运行此脚本"
        exit 1
    fi
    
    # 初始化变量
    SKIP_BUILD=false
    DEVICE_MODE="auto"
    GPU_AVAILABLE=false
    DOCKER_GPU_AVAILABLE=false
    COMPOSE_FILE=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --device)
                DEVICE_MODE="$2"
                if [[ ! "$DEVICE_MODE" =~ ^(auto|gpu|cpu)$ ]]; then
                    log_error "无效的设备模式: $DEVICE_MODE (支持: auto, gpu, cpu)"
                    exit 1
                fi
                shift 2
                ;;
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                echo "使用 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done
    
    # 执行部署步骤
    check_dependencies
    check_gpu_availability
    select_deployment_mode
    check_models
    setup_environment
    stop_containers
    build_images
    start_services
    wait_for_services
    health_check
    show_container_status
    show_service_info
    
    log_success "部署完成！🎉"
}

# 运行主函数
main "$@" 