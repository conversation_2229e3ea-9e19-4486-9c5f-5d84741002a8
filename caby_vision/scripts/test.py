#!/usr/bin/env python3
"""
Caby Vision 综合测试脚本
支持通过命令行参数选择不同的测试项目

测试功能：
- health: 健康检查
- predict: 猫咪个体识别
- performance: 性能测试
- all: 所有功能测试
"""

import requests
import base64
import json
import time
import sys
import os
import argparse
import statistics
from typing import Dict, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# API配置
DEFAULT_API_URL = "http://localhost:8001"
DEFAULT_API_KEY = os.getenv("CABY_VISION_API_KEY", "default_api_key")

class Colors:
    """控制台颜色"""
    GREEN = '\033[0;32m'
    RED = '\033[0;31m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color

class CabyVisionTester:
    """Caby Vision API 测试器"""
    
    def __init__(self, api_url: str, api_key: str, verbose: bool = False):
        self.api_url = api_url.rstrip('/')
        self.api_key = api_key
        self.verbose = verbose
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        self.test_results = {}
        self.lock = threading.Lock()
    
    def log(self, message: str, color: str = Colors.NC):
        """输出日志消息"""
        if self.verbose or color in [Colors.RED, Colors.GREEN]:
            print(f"{color}{message}{Colors.NC}")
    
    def log_success(self, message: str):
        """输出成功消息"""
        print(f"{Colors.GREEN}✅ {message}{Colors.NC}")
    
    def log_error(self, message: str):
        """输出错误消息"""
        print(f"{Colors.RED}❌ {message}{Colors.NC}")
    
    def log_warning(self, message: str):
        """输出警告消息"""
        print(f"{Colors.YELLOW}⚠️  {message}{Colors.NC}")
    
    def log_info(self, message: str):
        """输出信息消息"""
        print(f"{Colors.BLUE}ℹ️  {message}{Colors.NC}")
    
    def load_image_from_file(self, image_path: str) -> str:
        """从文件加载图像并返回base64编码"""
        try:
            from PIL import Image
            import io

            if not os.path.exists(image_path):
                self.log_error(f"图像文件不存在: {image_path}")
                return ""

            # 加载图像
            img = Image.open(image_path)

            # 转换为RGB格式（如果需要）
            if img.mode != 'RGB':
                img = img.convert('RGB')

            # 转换为base64
            buffer = io.BytesIO()
            img.save(buffer, format='JPEG')
            buffer.seek(0)

            return base64.b64encode(buffer.read()).decode()
        except ImportError:
            self.log_error("PIL库未安装，无法加载图像")
            return ""
        except Exception as e:
            self.log_error(f"加载图像失败: {e}")
            return ""

    def create_test_image(self, width: int = 224, height: int = 224) -> str:
        """创建测试图像并返回base64编码"""
        try:
            from PIL import Image
            import io

            # 创建一个简单的测试图像
            img = Image.new('RGB', (width, height), color='white')

            # 添加一些简单的图案
            from PIL import ImageDraw
            draw = ImageDraw.Draw(img)
            draw.rectangle([width//4, height//4, 3*width//4, 3*height//4], fill='gray')
            draw.ellipse([width//3, height//3, 2*width//3, 2*height//3], fill='black')

            buffer = io.BytesIO()
            img.save(buffer, format='JPEG')
            buffer.seek(0)

            return base64.b64encode(buffer.read()).decode()
        except ImportError:
            self.log_error("PIL库未安装，无法创建测试图像")
            return ""
        except Exception as e:
            self.log_error(f"创建测试图像失败: {e}")
            return ""
    
    def make_request(self, method: str, endpoint: str, data: dict = None, timeout: int = 30) -> Tuple[bool, dict]:
        """发送HTTP请求"""
        url = f"{self.api_url}{endpoint}"
        
        try:
            start_time = time.time()
            
            if method.upper() == "GET":
                response = requests.get(url, headers=self.headers, timeout=timeout)
            elif method.upper() == "POST":
                response = requests.post(url, headers=self.headers, json=data, timeout=timeout)
            else:
                return False, {"error": f"不支持的HTTP方法: {method}"}
            
            elapsed_time = (time.time() - start_time) * 1000  # ms
            
            if response.status_code == 200:
                result = response.json()
                result['_elapsed_time'] = elapsed_time
                return True, result
            else:
                return False, {
                    "error": f"HTTP {response.status_code}",
                    "detail": response.text,
                    "_elapsed_time": elapsed_time
                }
                
        except requests.RequestException as e:
            return False, {"error": f"请求异常: {str(e)}"}
        except json.JSONDecodeError as e:
            return False, {"error": f"JSON解析失败: {str(e)}"}
        except Exception as e:
            return False, {"error": f"未知错误: {str(e)}"}
    
    def test_health(self) -> bool:
        """测试健康检查"""
        self.log_info("测试健康检查...")
        
        success, result = self.make_request("GET", "/health")
        
        if success:
            elapsed = result.get('_elapsed_time', 0)
            status = result.get('status', 'unknown')
            model_loaded = result.get('model_loaded', False)
            device = result.get('device', 'unknown')
            classes = result.get('classes', [])
            num_classes = result.get('num_classes', 0)
            
            self.log_success(f"健康检查通过 ({elapsed:.1f}ms)")
            self.log(f"   状态: {status}", Colors.CYAN)
            self.log(f"   模型已加载: {model_loaded}", Colors.CYAN)
            self.log(f"   设备: {device}", Colors.CYAN)
            self.log(f"   支持类别: {classes}", Colors.CYAN)
            self.log(f"   类别数量: {num_classes}", Colors.CYAN)
            
            self.test_results['health'] = {
                'success': True,
                'elapsed_time': elapsed,
                'status': status,
                'model_loaded': model_loaded,
                'device': device,
                'classes': classes
            }
            return True
        else:
            self.log_error(f"健康检查失败: {result.get('error', 'unknown')}")
            self.test_results['health'] = {'success': False, 'error': result.get('error')}
            return False
    
    def test_predict(self, image_b64: str = None, image_path: str = None) -> bool:
        """测试猫咪个体识别"""
        self.log_info("测试猫咪个体识别...")

        if not image_b64:
            if image_path:
                self.log_info(f"使用外部图片: {image_path}")
                image_b64 = self.load_image_from_file(image_path)
            else:
                self.log_info("使用生成的测试图片")
                image_b64 = self.create_test_image()
            if not image_b64:
                return False
        
        payload = {
            "image": image_b64,
            "return_features": False,
            "return_confidence": True,
            "task": "predict"
        }
        
        success, result = self.make_request("POST", "/predict", payload, timeout=60)
        
        if success and result.get('success'):
            elapsed = result.get('_elapsed_time', 0)
            results = result.get('results', {})
            predicted_cat = results.get('predicted_cat', 'unknown')
            confidence = results.get('confidence', 0)
            class_probs = results.get('class_probabilities', {})
            
            self.log_success(f"预测成功 ({elapsed:.1f}ms)")
            self.log(f"   预测猫咪: {predicted_cat}", Colors.CYAN)
            self.log(f"   置信度: {confidence:.4f}", Colors.CYAN)
            
            if class_probs and self.verbose:
                self.log("   类别概率:", Colors.CYAN)
                for cat_name, prob in class_probs.items():
                    self.log(f"     {cat_name}: {prob:.4f}", Colors.CYAN)
            
            self.test_results['predict'] = {
                'success': True,
                'elapsed_time': elapsed,
                'predicted_cat': predicted_cat,
                'confidence': confidence,
                'class_probabilities': class_probs
            }
            return True
        else:
            error = result.get('error') or result.get('results', {}).get('error', 'unknown')
            self.log_error(f"预测失败: {error}")
            self.test_results['predict'] = {'success': False, 'error': error}
            return False
    

    

    
    def test_performance(self, num_requests: int = 10, concurrent: int = 3, image_path: str = None) -> bool:
        """测试性能"""
        self.log_info(f"测试性能 ({num_requests} 请求, {concurrent} 并发)...")

        if image_path:
            self.log_info(f"使用外部图片: {image_path}")
            image_b64 = self.load_image_from_file(image_path)
        else:
            self.log_info("使用生成的测试图片")
            image_b64 = self.create_test_image()
        if not image_b64:
            return False
        
        def single_request():
            payload = {
                "image": image_b64,
                "return_features": False,
                "return_confidence": True,
                "task": "predict"
            }
            start_time = time.time()
            success, result = self.make_request("POST", "/predict", payload, timeout=60)
            elapsed = (time.time() - start_time) * 1000
            
            if success and result.get('success'):
                return True, elapsed
            else:
                return False, elapsed
        
        # 执行并发测试
        times = []
        successes = 0
        failures = 0
        
        start_total = time.time()
        
        with ThreadPoolExecutor(max_workers=concurrent) as executor:
            futures = [executor.submit(single_request) for _ in range(num_requests)]
            
            for future in as_completed(futures):
                try:
                    success, elapsed = future.result()
                    times.append(elapsed)
                    if success:
                        successes += 1
                    else:
                        failures += 1
                except Exception as e:
                    failures += 1
                    self.log(f"请求异常: {e}", Colors.YELLOW)
        
        total_time = (time.time() - start_total) * 1000
        
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            median_time = statistics.median(times)
            
            self.log_success(f"性能测试完成 ({total_time:.1f}ms)")
            self.log(f"   成功请求: {successes}/{num_requests}", Colors.CYAN)
            self.log(f"   失败请求: {failures}", Colors.CYAN)
            self.log(f"   平均响应时间: {avg_time:.1f}ms", Colors.CYAN)
            self.log(f"   最小响应时间: {min_time:.1f}ms", Colors.CYAN)
            self.log(f"   最大响应时间: {max_time:.1f}ms", Colors.CYAN)
            self.log(f"   中位数响应时间: {median_time:.1f}ms", Colors.CYAN)
            self.log(f"   吞吐量: {num_requests / (total_time / 1000):.1f} req/s", Colors.CYAN)
            
            self.test_results['performance'] = {
                'success': True,
                'total_requests': num_requests,
                'successful_requests': successes,
                'failed_requests': failures,
                'avg_response_time': avg_time,
                'min_response_time': min_time,
                'max_response_time': max_time,
                'median_response_time': median_time,
                'throughput': num_requests / (total_time / 1000),
                'total_time': total_time
            }
            return successes > 0
        else:
            self.log_error("性能测试失败，没有成功的请求")
            self.test_results['performance'] = {'success': False, 'error': 'No successful requests'}
            return False
    
    def run_all_tests(self, image_path: str = None) -> Dict[str, bool]:
        """运行所有测试"""
        print(f"{Colors.WHITE}🐱 Caby Vision 综合测试{Colors.NC}")
        print("=" * 60)

        if image_path:
            print(f"{Colors.CYAN}📷 使用外部图片: {image_path}{Colors.NC}")
        else:
            print(f"{Colors.CYAN}📷 使用生成的测试图片{Colors.NC}")
        print()

        results = {}

        # 1. 健康检查
        results['health'] = self.test_health()
        print()

        if not results['health']:
            self.log_error("健康检查失败，停止后续测试")
            return results

        # 2. 猫咪识别
        results['predict'] = self.test_predict(image_path=image_path)
        print()

        # 3. 性能测试
        results['performance'] = self.test_performance(image_path=image_path)
        print()

        return results
    
    def print_summary(self, results: Dict[str, bool]):
        """打印测试总结"""
        print("=" * 60)
        print(f"{Colors.WHITE}📋 测试结果总结{Colors.NC}")
        print("=" * 60)
        
        for test_name, success in results.items():
            status = "✅ 通过" if success else "❌ 失败"
            test_display = {
                'health': '健康检查',
                'predict': '猫咪识别',
                'performance': '性能测试'
            }.get(test_name, test_name)
            
            print(f"   {test_display}: {status}")
        
        total_tests = len(results)
        passed_tests = sum(results.values())
        
        print()
        if passed_tests == total_tests:
            print(f"{Colors.GREEN}🎯 总体结果: 全部通过 ({passed_tests}/{total_tests}){Colors.NC}")
        else:
            print(f"{Colors.YELLOW}🎯 总体结果: 部分通过 ({passed_tests}/{total_tests}){Colors.NC}")
        
        # 保存详细结果到文件
        if self.test_results:
            try:
                with open('test_results.json', 'w', encoding='utf-8') as f:
                    json.dump(self.test_results, f, indent=2, ensure_ascii=False)
                print(f"{Colors.CYAN}📄 详细结果已保存到: test_results.json{Colors.NC}")
            except Exception as e:
                self.log_warning(f"保存结果文件失败: {e}")

def main():
    parser = argparse.ArgumentParser(
        description="Caby Vision 综合测试脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
测试项目:
  health      健康检查
  predict     猫咪个体识别
  performance 性能测试
  all         所有功能测试 (默认)

示例:
  python test.py                           # 运行所有测试
  python test.py health                    # 只运行健康检查
  python test.py predict performance      # 运行预测和性能测试
  python test.py performance --requests 20 --concurrent 5  # 性能测试
  python test.py predict --image /path/to/cat.jpg  # 使用外部图片测试预测
  python test.py all --image /path/to/cat.jpg      # 使用外部图片运行所有测试
        """
    )

    parser.add_argument(
        'tests',
        nargs='*',
        choices=['health', 'predict', 'performance', 'all'],
        default=['all'],
        help='要运行的测试项目'
    )

    parser.add_argument('--url', default=DEFAULT_API_URL, help='API基础URL')
    parser.add_argument('--api-key', default=DEFAULT_API_KEY, help='API密钥')
    parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')
    parser.add_argument('--requests', type=int, default=10, help='性能测试请求数量')
    parser.add_argument('--concurrent', type=int, default=3, help='性能测试并发数')
    parser.add_argument('--image', '-i', help='指定外部图片文件路径（用于predict、performance测试）')
    
    args = parser.parse_args()

    # 验证图片文件（如果提供）
    if args.image and not os.path.exists(args.image):
        print(f"{Colors.RED}❌ 图片文件不存在: {args.image}{Colors.NC}")
        sys.exit(1)

    # 创建测试器
    tester = CabyVisionTester(args.url, args.api_key, args.verbose)

    print(f"{Colors.CYAN}🌐 API URL: {args.url}{Colors.NC}")
    print(f"{Colors.CYAN}🔑 API Key: {args.api_key}{Colors.NC}")
    if args.image:
        print(f"{Colors.CYAN}📷 外部图片: {args.image}{Colors.NC}")
    print()

    # 确定要运行的测试
    if 'all' in args.tests:
        results = tester.run_all_tests(image_path=args.image)
    else:
        results = {}

        for test in args.tests:
            if test == 'health':
                results['health'] = tester.test_health()
            elif test == 'predict':
                results['predict'] = tester.test_predict(image_path=args.image)

            elif test == 'performance':
                results['performance'] = tester.test_performance(args.requests, args.concurrent, image_path=args.image)

            print()
    
    # 打印总结
    tester.print_summary(results)
    
    # 返回退出码
    all_passed = all(results.values())
    sys.exit(0 if all_passed else 1)

if __name__ == "__main__":
    main() 