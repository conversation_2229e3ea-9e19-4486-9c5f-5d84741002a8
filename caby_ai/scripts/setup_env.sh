#!/bin/bash

# 环境变量设置脚本
# 用于初始化.env文件并设置必要的环境变量

# 颜色代码
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  Caby AI + LitServe 环境配置${NC}"
echo -e "${BLUE}========================================${NC}"

# 检查.env文件是否存在
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}创建.env文件...${NC}"
    cp env.example .env
    echo -e "${GREEN}✓ .env文件已创建${NC}"
else
    echo -e "${YELLOW}检测到现有.env文件${NC}"
fi

# 从config.yaml读取service_token
if [ -f "config/config.yaml" ]; then
    echo -e "${YELLOW}从config.yaml读取service_token...${NC}"
    SERVICE_TOKEN=$(grep "service_token:" config/config.yaml | sed 's/.*service_token: *"\([^"]*\)".*/\1/')
    
    if [ -n "$SERVICE_TOKEN" ]; then
        echo -e "${GREEN}✓ 找到service_token: $SERVICE_TOKEN${NC}"
        
        # 更新.env文件中的CABY_AI_SERVICE_TOKEN
        if grep -q "CABY_AI_SERVICE_TOKEN=" .env; then
            sed -i.bak "s/CABY_AI_SERVICE_TOKEN=.*/CABY_AI_SERVICE_TOKEN=$SERVICE_TOKEN/" .env
            echo -e "${GREEN}✓ 已更新.env文件中的CABY_AI_SERVICE_TOKEN${NC}"
        else
            echo "CABY_AI_SERVICE_TOKEN=$SERVICE_TOKEN" >> .env
            echo -e "${GREEN}✓ 已添加CABY_AI_SERVICE_TOKEN到.env文件${NC}"
        fi
    else
        echo -e "${RED}✗ 无法从config.yaml读取service_token${NC}"
    fi
else
    echo -e "${RED}✗ config/config.yaml文件不存在${NC}"
fi

# 检查必要的环境变量
echo -e "\n${YELLOW}检查环境变量配置...${NC}"

# 源化.env文件来检查变量
source .env

# 检查必要的变量
REQUIRED_VARS=("CABY_AI_SERVICE_TOKEN" "LITSERVE_API_KEY")
MISSING_VARS=()

for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        MISSING_VARS+=("$var")
        echo -e "${RED}✗ $var 未设置${NC}"
    else
        echo -e "${GREEN}✓ $var 已设置${NC}"
    fi
done

if [ ${#MISSING_VARS[@]} -eq 0 ]; then
    echo -e "\n${GREEN}✅ 所有必要的环境变量都已设置！${NC}"
    echo -e "${YELLOW}您现在可以运行:${NC}"
    echo -e "  ./scripts/quick_deploy.sh     # 部署服务"
    echo -e "  ./scripts/test_litserve.sh    # 测试LitServe API"
else
    echo -e "\n${RED}❌ 以下环境变量需要手动设置:${NC}"
    for var in "${MISSING_VARS[@]}"; do
        echo -e "${RED}  - $var${NC}"
    done
    echo -e "\n${YELLOW}请编辑.env文件并设置这些变量，然后重新运行此脚本${NC}"
    echo -e "${YELLOW}编辑命令: nano .env${NC}"
fi

echo -e "\n${BLUE}环境配置完成！${NC}" 