#!/usr/bin/env python3
"""
测试猫咪个体识别API的脚本
"""

import requests
import base64
import json
import sys
import os
from pathlib import Path

# API配置
API_BASE_URL = "http://localhost:8765"
API_TOKEN = "caby-token"  # 从环境变量或配置文件获取

def encode_image_to_base64(image_path):
    """将图像文件编码为base64"""
    with open(image_path, 'rb') as f:
        return base64.b64encode(f.read()).decode('utf-8')

def test_recognition_api(image_path, user_id, model_type="infrared"):
    """测试个体识别API"""
    url = f"{API_BASE_URL}/api/v1/recognition/identify"
    
    headers = {
        "Authorization": f"Bearer {API_TOKEN}"
    }
    
    # 准备文件和数据
    with open(image_path, 'rb') as f:
        files = {
            'image': (os.path.basename(image_path), f, 'image/jpeg')
        }
        data = {
            'user_id': user_id,
            'model_type': model_type
        }
        
        response = requests.post(url, headers=headers, files=files, data=data)
    
    print(f"Recognition API Response ({response.status_code}):")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    return response.json()

def test_register_api(image_path, user_id, cat_id, cat_name, model_type="infrared"):
    """测试猫咪注册API"""
    url = f"{API_BASE_URL}/api/v1/recognition/register"
    
    headers = {
        "Authorization": f"Bearer {API_TOKEN}"
    }
    
    # 准备文件和数据
    with open(image_path, 'rb') as f:
        files = {
            'image': (os.path.basename(image_path), f, 'image/jpeg')
        }
        data = {
            'user_id': user_id,
            'cat_id': cat_id,
            'cat_name': cat_name,
            'model_type': model_type
        }
        
        response = requests.post(url, headers=headers, files=files, data=data)
    
    print(f"Register API Response ({response.status_code}):")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    return response.json()

def test_get_user_cats(user_id, model_type="infrared"):
    """测试获取用户猫咪列表API"""
    url = f"{API_BASE_URL}/api/v1/recognition/cats"
    
    headers = {
        "Authorization": f"Bearer {API_TOKEN}"
    }
    
    params = {
        'user_id': user_id,
        'model_type': model_type
    }
    
    response = requests.get(url, headers=headers, params=params)
    
    print(f"Get User Cats API Response ({response.status_code}):")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    return response.json()

def test_vision_api_direct(image_path, task="predict"):
    """直接测试Vision API"""
    url = f"http://localhost:8001/{task}"
    
    headers = {
        "Authorization": "Bearer default_api_key",
        "Content-Type": "application/json"
    }
    
    image_base64 = encode_image_to_base64(image_path)
    
    payload = {
        "image": image_base64,
        "return_features": True,
        "return_confidence": True,
        "task": task
    }
    
    response = requests.post(url, headers=headers, json=payload)
    
    print(f"Vision API ({task}) Response ({response.status_code}):")
    if response.status_code == 200:
        result = response.json()
        print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print(f"Error: {response.text}")
    
    return response.json() if response.status_code == 200 else None

def main():
    if len(sys.argv) < 2:
        print("Usage: python test_recognition.py <command> [args...]")
        print("Commands:")
        print("  recognize <image_path> <user_id> [model_type]")
        print("  register <image_path> <user_id> <cat_id> <cat_name> [model_type]")
        print("  list <user_id> [model_type]")
        print("  vision <image_path> [task]")
        return
    
    command = sys.argv[1]
    
    try:
        if command == "recognize":
            if len(sys.argv) < 4:
                print("Usage: python test_recognition.py recognize <image_path> <user_id> [model_type]")
                return
            
            image_path = sys.argv[2]
            user_id = sys.argv[3]
            model_type = sys.argv[4] if len(sys.argv) > 4 else "infrared"
            
            if not os.path.exists(image_path):
                print(f"Image file not found: {image_path}")
                return
            
            test_recognition_api(image_path, user_id, model_type)
        
        elif command == "register":
            if len(sys.argv) < 6:
                print("Usage: python test_recognition.py register <image_path> <user_id> <cat_id> <cat_name> [model_type]")
                return
            
            image_path = sys.argv[2]
            user_id = sys.argv[3]
            cat_id = sys.argv[4]
            cat_name = sys.argv[5]
            model_type = sys.argv[6] if len(sys.argv) > 6 else "infrared"
            
            if not os.path.exists(image_path):
                print(f"Image file not found: {image_path}")
                return
            
            test_register_api(image_path, user_id, cat_id, cat_name, model_type)
        
        elif command == "list":
            if len(sys.argv) < 3:
                print("Usage: python test_recognition.py list <user_id> [model_type]")
                return
            
            user_id = sys.argv[2]
            model_type = sys.argv[3] if len(sys.argv) > 3 else "infrared"
            
            test_get_user_cats(user_id, model_type)
        
        elif command == "vision":
            if len(sys.argv) < 3:
                print("Usage: python test_recognition.py vision <image_path> [task]")
                return
            
            image_path = sys.argv[2]
            task = sys.argv[3] if len(sys.argv) > 3 else "predict"
            
            if not os.path.exists(image_path):
                print(f"Image file not found: {image_path}")
                return
            
            test_vision_api_direct(image_path, task)
        
        else:
            print(f"Unknown command: {command}")
            print("Available commands: recognize, register, list, vision")
    
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
