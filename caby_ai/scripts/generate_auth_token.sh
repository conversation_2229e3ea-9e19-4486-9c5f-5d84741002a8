#!/bin/bash
# Generate a secure random token suitable for service-to-service authentication

# Generate 32 random bytes and encode them in base64
TOKEN=$(head /dev/urandom | tr -dc A-Za-z0-9 | head -c 32)

echo "Generated Auth Token:"
echo "$TOKEN"

# Optional: You might want to automatically update the config.yaml
# CONFIG_FILE="../config/config.yaml"
# if [ -f "$CONFIG_FILE" ]; then
#   # Use sed to replace the placeholder token (be careful with special characters in the token)
#   # This example assumes the placeholder line is exactly 'service_token: "your_secure_service_to_service_token"'
#   sed -i "s/service_token: "your_secure_service_to_service_token"/service_token: "$TOKEN"/" "$CONFIG_FILE"
#   echo "Updated $CONFIG_FILE with the new token."
# else
#   echo "Warning: $CONFIG_FILE not found. Please update the token manually."
# fi
