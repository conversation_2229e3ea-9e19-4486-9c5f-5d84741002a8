#!/bin/bash

# ==============================================
# 🧪 Caby AI 人脸检测API测试脚本 (Shell版本)
# ==============================================

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_TOKEN="03U66tGbSQtHGrh9IyBDjRYaSeQukFga"
BASE_URL="http://localhost:8765"
TIMEOUT=30

# 批量测试配置
DEFAULT_CONCURRENT=5
DEFAULT_REQUESTS=10
BATCH_LOG_DIR="/tmp/caby_batch_test"

# 检查必要的依赖
check_dependencies() {
    local missing_deps=()
    
    # 检查curl
    if ! command -v curl >/dev/null 2>&1; then
        missing_deps+=("curl")
    fi
    
    # 检查bc（用于数学计算）
    if ! command -v bc >/dev/null 2>&1; then
        missing_deps+=("bc")
    fi
    
    # 检查find
    if ! command -v find >/dev/null 2>&1; then
        missing_deps+=("find")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo -e "${RED}❌ 缺少必要的依赖工具: ${missing_deps[*]}${NC}"
        echo -e "${YELLOW}💡 请安装缺少的工具:${NC}"
        echo -e "${YELLOW}  Ubuntu/Debian: sudo apt-get install curl bc findutils${NC}"
        echo -e "${YELLOW}  CentOS/RHEL: sudo yum install curl bc findutils${NC}"
        echo -e "${YELLOW}  macOS: brew install bc${NC}"
        exit 1
    fi
}

# 获取服务鉴权token
get_service_token() {
    local token=""
    
    # 1. 优先从环境变量获取
    if [ ! -z "$CABY_AI_SERVICE_TOKEN" ]; then
        token="$CABY_AI_SERVICE_TOKEN"
        echo -e "${GREEN}🔑 使用环境变量中的token${NC}" >&2
        echo "$token"
        return
    fi
    
    # 2. 从配置文件获取
    local config_paths=(
        "config/config.yaml"
        "../config/config.yaml"
        "/app/config/config.yaml"
    )
    
    for config_path in "${config_paths[@]}"; do
        if [ -f "$config_path" ]; then
            # 尝试从yaml文件中提取token，处理多种格式
            token=$(grep -E "service_token.*:" "$config_path" | head -1 | sed 's/.*service_token[[:space:]]*:[[:space:]]*//' | sed 's/[[:space:]]*#.*//' | tr -d '"' | tr -d "'" | xargs)
            if [ ! -z "$token" ]; then
                echo -e "${YELLOW}📄 使用配置文件中的token: $config_path${NC}" >&2
                echo "$token"
                return
            fi
        fi
    done
    
    # 3. 使用默认token
    echo -e "${YELLOW}⚠️  未找到配置的token，使用默认token${NC}" >&2
    echo "$DEFAULT_TOKEN"
}

# 测试主服务健康检查（不需要鉴权）
test_main_health() {
    echo -e "${BLUE}🏥 测试Caby AI主服务健康检查...${NC}"
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$BASE_URL/health" --connect-timeout $TIMEOUT)
    local http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 主服务健康检查通过!${NC}"
        local status=$(cat /tmp/health_response.json)
        echo -e "${GREEN}📊 响应: $status${NC}"
        rm -f /tmp/health_response.json
        return 0
    else
        echo -e "${RED}❌ 主服务健康检查失败: HTTP $http_code${NC}"
        if [ -f /tmp/health_response.json ]; then
            echo -e "${RED}响应内容: $(cat /tmp/health_response.json)${NC}"
            rm -f /tmp/health_response.json
        fi
        return 1
    fi
}

# 测试Vision健康检查（需要鉴权）
test_vision_health() {
    local token="$1"
    echo -e "${BLUE}🏥 测试Vision健康检查...${NC}"
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/vision_health.json \
        -H "Authorization: Bearer $token" \
        "$BASE_URL/api/v1/vision/health" \
        --connect-timeout $TIMEOUT)
    local http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ Vision健康检查通过!${NC}"
        local status=$(cat /tmp/vision_health.json)
        echo -e "${GREEN}📊 响应: $status${NC}"
        rm -f /tmp/vision_health.json
        return 0
    elif [ "$http_code" = "401" ]; then
        echo -e "${RED}❌ Vision健康检查鉴权失败${NC}"
        echo -e "${YELLOW}当前使用的token: ${token:0:10}...${token: -10}${NC}"
        echo -e "${YELLOW}请检查以下配置:${NC}"
        echo -e "${YELLOW}  1. 环境变量 CABY_AI_SERVICE_TOKEN${NC}"
        echo -e "${YELLOW}  2. config/config.yaml 中的 auth.service_token${NC}"
        rm -f /tmp/vision_health.json
        return 1
    else
        echo -e "${RED}❌ Vision健康检查失败: HTTP $http_code${NC}"
        if [ -f /tmp/vision_health.json ]; then
            echo -e "${RED}响应内容: $(cat /tmp/vision_health.json)${NC}"
            rm -f /tmp/vision_health.json
        fi
        return 1
    fi
}

# 测试人脸检测API
test_face_detection() {
    local token="$1"
    local image_path="$2"
    
    echo -e "${BLUE}📸 测试人脸检测API...${NC}"
    echo -e "${BLUE}使用图像: $image_path${NC}"
    
    if [ ! -f "$image_path" ]; then
        echo -e "${RED}❌ 图像文件不存在: $image_path${NC}"
        return 1
    fi
    
    # 检查文件大小
    local file_size=$(wc -c < "$image_path")
    if [ $file_size -gt 10485760 ]; then  # 10MB
        echo -e "${YELLOW}⚠️  图像文件较大 ($(($file_size/1024/1024))MB)，可能影响传输时间${NC}"
    fi
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/detection_response.json \
        -H "Authorization: Bearer $token" \
        -F "image=@$image_path" \
        -F "task=detect" \
        -F "conf_threshold=0.2" \
        -F "iou_threshold=0.5" \
        -F "return_aligned=true" \
        "$BASE_URL/api/v1/vision/detect/face" \
        --connect-timeout $TIMEOUT)
    local http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 人脸检测成功!${NC}"
        
        # 解析响应
        local result=$(cat /tmp/detection_response.json)
        local face_count=$(echo "$result" | grep -o '"face_count":[0-9]*' | cut -d':' -f2)
        local process_time=$(echo "$result" | grep -o '"process_time":[0-9.]*' | cut -d':' -f2)
        
        echo -e "${GREEN}👥 检测到 $face_count 个人脸${NC}"
        echo -e "${GREEN}⏱️  处理时间: ${process_time}ms${NC}"
        
        # 显示部分响应内容
        echo -e "${GREEN}📊 API响应概要:${NC}"
        echo "$result" | head -c 500
        if [ ${#result} -gt 500 ]; then
            echo "..."
        fi
        echo ""
        
        rm -f /tmp/detection_response.json
        return 0
    elif [ "$http_code" = "401" ]; then
        echo -e "${RED}❌ 人脸检测鉴权失败${NC}"
        echo -e "${YELLOW}当前使用的token: ${token:0:10}...${token: -10}${NC}"
        rm -f /tmp/detection_response.json
        return 1
    else
        echo -e "${RED}❌ 人脸检测失败: HTTP $http_code${NC}"
        if [ -f /tmp/detection_response.json ]; then
            echo -e "${RED}响应内容: $(cat /tmp/detection_response.json)${NC}"
            rm -f /tmp/detection_response.json
        fi
        return 1
    fi
}

# 测试嵌入向量提取API
test_embeddings_extraction() {
    local token="$1"
    local image_path="$2"
    
    echo -e "${BLUE}🔢 测试嵌入向量提取API...${NC}"
    
    if [ ! -f "$image_path" ]; then
        echo -e "${RED}❌ 图像文件不存在: $image_path${NC}"
        return 1
    fi
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/embeddings_response.json \
        -H "Authorization: Bearer $token" \
        -F "image=@$image_path" \
        -F "task=embeddings" \
        -F "conf_threshold=0.2" \
        -F "iou_threshold=0.5" \
        "$BASE_URL/api/v1/vision/detect/face" \
        --connect-timeout $TIMEOUT)
    local http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 嵌入向量提取成功!${NC}"
        
        # 解析响应
        local result=$(cat /tmp/embeddings_response.json)
        local embeddings_count=$(echo "$result" | grep -o '"embeddings":\[' | wc -l)
        local process_time=$(echo "$result" | grep -o '"process_time":[0-9.]*' | cut -d':' -f2)
        
        echo -e "${GREEN}🔢 提取到 $embeddings_count 个人脸的嵌入向量${NC}"
        echo -e "${GREEN}⏱️  处理时间: ${process_time}ms${NC}"
        
        # 显示向量维度信息
        local embedding_sample=$(echo "$result" | grep -o '"embedding":\[[^]]*\]' | head -1)
        if [ ! -z "$embedding_sample" ]; then
            local dim_count=$(echo "$embedding_sample" | grep -o ',' | wc -l)
            echo -e "${GREEN}📏 向量维度: $((dim_count + 1))${NC}"
        fi
        
        rm -f /tmp/embeddings_response.json
        return 0
    elif [ "$http_code" = "401" ]; then
        echo -e "${RED}❌ 嵌入向量提取鉴权失败${NC}"
        rm -f /tmp/embeddings_response.json
        return 1
    else
        echo -e "${RED}❌ 嵌入向量提取失败: HTTP $http_code${NC}"
        if [ -f /tmp/embeddings_response.json ]; then
            echo -e "${RED}响应内容: $(cat /tmp/embeddings_response.json)${NC}"
            rm -f /tmp/embeddings_response.json
        fi
        return 1
    fi
}

# 批量测试单个API端点
batch_test_api() {
    local token="$1"
    local image_path="$2"
    local endpoint="$3" # "detect" or "embeddings"
    local concurrent="$4"
    local total_requests="$5"
    local batch_id="$6"
    
    echo -e "${BLUE}🚀 开始批量测试 $endpoint API${NC}"
    echo -e "${BLUE}📊 并发数: $concurrent, 总请求数: $total_requests${NC}"
    
    # 创建批量测试日志目录
    mkdir -p "$BATCH_LOG_DIR/$batch_id"
    
    # 准备请求参数
    local form_data="-F image=@$image_path"
    if [ "$endpoint" = "detect" ]; then
        form_data="$form_data -F task=detect -F conf_threshold=0.2 -F iou_threshold=0.5 -F return_aligned=true"
    else
        form_data="$form_data -F task=embeddings -F conf_threshold=0.2 -F iou_threshold=0.5"
    fi
    
    # 创建请求函数
    local request_func="send_request_$batch_id"
    eval "$request_func() {
        local req_id=\$1
        local start_time=\$(date +%s.%N)
        local response=\$(curl -w '%{http_code},%{time_total},%{size_download}' -o '$BATCH_LOG_DIR/$batch_id/resp_\$req_id.json' -s \\
            -H 'Authorization: Bearer $token' \\
            $form_data \\
            '$BASE_URL/api/v1/vision/detect/face' \\
            --connect-timeout $TIMEOUT 2>/dev/null)
        local end_time=\$(date +%s.%N)
        
        local http_code=\$(echo \"\$response\" | cut -d',' -f1)
        local curl_time=\$(echo \"\$response\" | cut -d',' -f2)
        local response_size=\$(echo \"\$response\" | cut -d',' -f3)
        local total_time=\$(echo \"\$end_time - \$start_time\" | bc -l)
        
        echo \"\$req_id,\$http_code,\$curl_time,\$response_size,\$total_time\" >> '$BATCH_LOG_DIR/$batch_id/results.csv'
    }"
    
    # 初始化结果文件
    echo "request_id,http_code,curl_time,response_size,total_time" > "$BATCH_LOG_DIR/$batch_id/results.csv"
    
    # 启动并发请求
    local pids=()
    local request_count=0
    local start_batch_time=$(date +%s.%N)
    
    for ((i=1; i<=total_requests; i++)); do
        # 控制并发数
        while [ ${#pids[@]} -ge $concurrent ]; do
            # 等待任意一个进程完成
            for j in "${!pids[@]}"; do
                if ! kill -0 ${pids[j]} 2>/dev/null; then
                    wait ${pids[j]}
                    unset pids[j]
                    break
                fi
            done
            sleep 0.01
        done
        
        # 启动新请求
        $request_func $i &
        pids+=($!)
        request_count=$((request_count + 1))
        
        # 进度显示
        if [ $((i % 10)) -eq 0 ] || [ $i -eq $total_requests ]; then
            echo -e "${YELLOW}📈 进度: $i/$total_requests${NC}"
        fi
    done
    
    # 等待所有请求完成
    echo -e "${YELLOW}⏳ 等待所有请求完成...${NC}"
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    local end_batch_time=$(date +%s.%N)
    local total_batch_time=$(echo "$end_batch_time - $start_batch_time" | bc -l)
    
    # 分析结果
    analyze_batch_results "$batch_id" "$endpoint" "$total_batch_time" "$total_requests"
}

# 分析批量测试结果
analyze_batch_results() {
    local batch_id="$1"
    local endpoint="$2"
    local total_time="$3"
    local total_requests="$4"
    local results_file="$BATCH_LOG_DIR/$batch_id/results.csv"
    
    if [ ! -f "$results_file" ]; then
        echo -e "${RED}❌ 结果文件不存在${NC}"
        return 1
    fi
    
    echo -e "\n${BLUE}📊 批量测试结果分析 ($endpoint)${NC}"
    echo -e "${BLUE}================================================${NC}"
    
    # 统计成功率
    local success_count=$(awk -F',' 'NR>1 && $2==200 {count++} END {print count+0}' "$results_file")
    local success_rate=$(echo "scale=2; $success_count * 100 / $total_requests" | bc -l)
    
    # 统计HTTP状态码分布
    echo -e "${GREEN}📈 请求统计:${NC}"
    echo -e "  总请求数: $total_requests"
    echo -e "  成功请求: $success_count"
    echo -e "  成功率: ${success_rate}%"
    echo -e "  总耗时: $(printf "%.2f" $total_time)秒"
    echo -e "  平均QPS: $(echo "scale=2; $total_requests / $total_time" | bc -l)"
    
    echo -e "\n${GREEN}📊 HTTP状态码分布:${NC}"
    awk -F',' 'NR>1 {codes[$2]++} END {for(code in codes) printf "  %s: %d次\n", code, codes[code]}' "$results_file" | sort
    
    if [ $success_count -gt 0 ]; then
        # 响应时间统计（只统计成功的请求）
        echo -e "\n${GREEN}⏱️  响应时间统计 (成功请求):${NC}"
        awk -F',' 'NR>1 && $2==200 {print $3}' "$results_file" | sort -n > "/tmp/success_times_$batch_id.txt"
        
        local min_time=$(head -1 "/tmp/success_times_$batch_id.txt")
        local max_time=$(tail -1 "/tmp/success_times_$batch_id.txt")
        local avg_time=$(awk '{sum+=$1} END {printf "%.3f", sum/NR}' "/tmp/success_times_$batch_id.txt")
        
        # 计算百分位数
        local p50_line=$(echo "($success_count + 1) * 0.5 / 1" | bc)
        local p90_line=$(echo "($success_count + 1) * 0.9 / 1" | bc)
        local p95_line=$(echo "($success_count + 1) * 0.95 / 1" | bc)
        local p99_line=$(echo "($success_count + 1) * 0.99 / 1" | bc)
        
        local p50=$(sed -n "${p50_line}p" "/tmp/success_times_$batch_id.txt")
        local p90=$(sed -n "${p90_line}p" "/tmp/success_times_$batch_id.txt")
        local p95=$(sed -n "${p95_line}p" "/tmp/success_times_$batch_id.txt")
        local p99=$(sed -n "${p99_line}p" "/tmp/success_times_$batch_id.txt")
        
        echo -e "  最小值: ${min_time}s"
        echo -e "  最大值: ${max_time}s"
        echo -e "  平均值: ${avg_time}s"
        echo -e "  P50: ${p50}s"
        echo -e "  P90: ${p90}s"
        echo -e "  P95: ${p95}s"
        echo -e "  P99: ${p99}s"
        
        rm -f "/tmp/success_times_$batch_id.txt"
        
        # 响应大小统计
        echo -e "\n${GREEN}📦 响应大小统计:${NC}"
        local avg_size=$(awk -F',' 'NR>1 && $2==200 {sum+=$4; count++} END {printf "%.0f", sum/count}' "$results_file")
        local total_size=$(awk -F',' 'NR>1 && $2==200 {sum+=$4} END {printf "%.0f", sum}' "$results_file")
        echo -e "  平均响应大小: $(echo "$avg_size" | numfmt --to=iec-i --suffix=B)"
        echo -e "  总传输量: $(echo "$total_size" | numfmt --to=iec-i --suffix=B)"
    fi
    
    # 清理临时文件（可选，保留用于调试）
    echo -e "\n${YELLOW}📁 详细日志保存在: $BATCH_LOG_DIR/$batch_id/${NC}"
}

# 批量测试目录中的图片
batch_test_directory() {
    local token="$1"
    local dir_path="$2"
    local concurrent="$3"
    local requests_per_image="$4"
    
    echo -e "${BLUE}📁 开始批量测试目录: $dir_path${NC}"
    
    if [ ! -d "$dir_path" ]; then
        echo -e "${RED}❌ 目录不存在: $dir_path${NC}"
        return 1
    fi
    
    # 查找图片文件
    local image_files=()
    while IFS= read -r -d '' file; do
        image_files+=("$file")
    done < <(find "$dir_path" -type f \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" -o -iname "*.bmp" -o -iname "*.webp" \) -print0)
    
    if [ ${#image_files[@]} -eq 0 ]; then
        echo -e "${RED}❌ 目录中未找到图片文件${NC}"
        return 1
    fi
    
    echo -e "${GREEN}📸 找到 ${#image_files[@]} 个图片文件${NC}"
    
    local total_tests=0
    local successful_tests=0
    
    for ((i=0; i<${#image_files[@]}; i++)); do
        local image_file="${image_files[i]}"
        local filename=$(basename "$image_file")
        local batch_id="batch_dir_${i}_$(date +%s)"
        
        echo -e "\n${YELLOW}📸 测试图片 $((i+1))/${#image_files[@]}: $filename${NC}"
        
        # 对每个图片执行批量测试
        if batch_test_api "$token" "$image_file" "detect" "$concurrent" "$requests_per_image" "$batch_id"; then
            successful_tests=$((successful_tests + 1))
        fi
        total_tests=$((total_tests + 1))
    done
    
    echo -e "\n${BLUE}📊 目录批量测试总结:${NC}"
    echo -e "  测试图片数: $total_tests"
    echo -e "  成功图片数: $successful_tests"
    echo -e "  成功率: $(echo "scale=2; $successful_tests * 100 / $total_tests" | bc -l)%"
}

# 显示帮助信息
show_help() {
    echo "🧪 Caby AI 人脸检测API测试脚本"
    echo ""
    echo "用法:"
    echo "  $0 <image_path_or_directory> [options]"
    echo ""
    echo "参数:"
    echo "  image_path_or_directory  测试图像文件路径或目录路径"
    echo ""
    echo "基础选项:"
    echo "  -t, --token              指定鉴权token (覆盖环境变量和配置文件)"
    echo "  -u, --url                指定API基础URL (默认: http://localhost:8765)"
    echo "  -h, --help               显示此帮助信息"
    echo ""
    echo "批量测试选项:"
    echo "  -b, --batch              启用批量压力测试模式"
    echo "  -c, --concurrent NUM     并发请求数 (默认: $DEFAULT_CONCURRENT)"
    echo "  -r, --requests NUM       总请求数 (默认: $DEFAULT_REQUESTS)"
    echo "  -d, --directory          批量测试目录模式 (自动检测目录)"
    echo "  --requests-per-image NUM 目录模式下每个图片的请求数 (默认: 5)"
    echo ""
    echo "示例:"
    echo "  # 基础测试"
    echo "  $0 ~/Pictures/test.jpg"
    echo ""
    echo "  # 单图片压力测试"
    echo "  $0 ~/Pictures/test.jpg --batch --concurrent 10 --requests 100"
    echo ""
    echo "  # 目录批量测试"
    echo "  $0 ~/Pictures/ --batch --directory --concurrent 5 --requests-per-image 10"
    echo ""
    echo "  # 自定义服务器和token"
    echo "  $0 test.jpg --batch --url http://*************:8765 --token your_token"
    echo ""
    echo "环境变量:"
    echo "  CABY_AI_SERVICE_TOKEN    服务鉴权token"
}

# 主函数
main() {
    local image_path=""
    local custom_token=""
    local batch_mode=false
    local directory_mode=false
    local concurrent="$DEFAULT_CONCURRENT"
    local total_requests="$DEFAULT_REQUESTS"
    local requests_per_image=5
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--token)
                custom_token="$2"
                shift 2
                ;;
            -u|--url)
                BASE_URL="$2"
                shift 2
                ;;
            -b|--batch)
                batch_mode=true
                shift
                ;;
            -d|--directory)
                directory_mode=true
                shift
                ;;
            -c|--concurrent)
                concurrent="$2"
                shift 2
                ;;
            -r|--requests)
                total_requests="$2"
                shift 2
                ;;
            --requests-per-image)
                requests_per_image="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -*)
                echo -e "${RED}❌ 未知选项: $1${NC}"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$image_path" ]; then
                    image_path="$1"
                else
                    echo -e "${RED}❌ 多余的参数: $1${NC}"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查依赖
    check_dependencies
    
    # 检查必需参数
    if [ -z "$image_path" ]; then
        echo -e "${RED}❌ 缺少图像文件路径或目录路径${NC}"
        show_help
        exit 1
    fi
    
    # 验证参数
    if ! [[ "$concurrent" =~ ^[0-9]+$ ]] || [ "$concurrent" -lt 1 ]; then
        echo -e "${RED}❌ 并发数必须是正整数${NC}"
        exit 1
    fi
    
    if ! [[ "$total_requests" =~ ^[0-9]+$ ]] || [ "$total_requests" -lt 1 ]; then
        echo -e "${RED}❌ 请求数必须是正整数${NC}"
        exit 1
    fi
    
    if ! [[ "$requests_per_image" =~ ^[0-9]+$ ]] || [ "$requests_per_image" -lt 1 ]; then
        echo -e "${RED}❌ 每图片请求数必须是正整数${NC}"
        exit 1
    fi
    
    # 检查路径类型（文件或目录）
    if [ -f "$image_path" ]; then
        if [ "$directory_mode" = true ]; then
            echo -e "${YELLOW}⚠️  指定了目录模式但提供的是文件路径，将使用文件模式${NC}"
            directory_mode=false
        fi
    elif [ -d "$image_path" ]; then
        if [ "$batch_mode" = true ] && [ "$directory_mode" = false ]; then
            echo -e "${YELLOW}⚠️  检测到目录路径，自动启用目录模式${NC}"
            directory_mode=true
        fi
    else
        echo -e "${RED}❌ 路径不存在或不是有效的文件/目录: $image_path${NC}"
        exit 1
    fi
    
    # 获取鉴权token
    local token=""
    if [ ! -z "$custom_token" ]; then
        token="$custom_token"
        echo -e "${GREEN}🔑 使用命令行指定的token${NC}"
    else
        token=$(get_service_token)
    fi
    
    # 检查是否为批量测试模式
    if [ "$batch_mode" = true ]; then
        echo -e "${BLUE}🚀 开始批量压力测试 Caby AI 人脸检测API${NC}"
        echo -e "${BLUE}================================================${NC}"
        echo -e "${BLUE}📍 API地址: $BASE_URL${NC}"
        echo -e "${BLUE}🔑 使用token: ${token:0:10}...${token: -10}${NC}"
        
        if [ "$directory_mode" = true ]; then
            echo -e "${BLUE}📁 测试模式: 目录批量测试${NC}"
            echo -e "${BLUE}📊 每图片请求数: $requests_per_image, 并发数: $concurrent${NC}"
            echo ""
            
            batch_test_directory "$token" "$image_path" "$concurrent" "$requests_per_image"
        else
            echo -e "${BLUE}📸 测试模式: 单图片压力测试${NC}"
            echo -e "${BLUE}📊 总请求数: $total_requests, 并发数: $concurrent${NC}"
            echo -e "${BLUE}📸 测试图像: $image_path${NC}"
            echo ""
            
            # 先做基础健康检查
            echo -e "${YELLOW}🏥 执行基础健康检查...${NC}"
            if ! test_main_health || ! test_vision_health "$token"; then
                echo -e "${RED}❌ 基础健康检查失败，建议先解决基础问题再进行压力测试${NC}"
                exit 1
            fi
            echo ""
            
            # 执行人脸检测压力测试
            local batch_id="batch_single_$(date +%s)"
            if batch_test_api "$token" "$image_path" "detect" "$concurrent" "$total_requests" "$batch_id"; then
                echo -e "\n${GREEN}🎉 单图片人脸检测压力测试完成!${NC}"
                
                # 可选：同时测试嵌入向量提取
                echo -e "\n${YELLOW}🔢 是否继续测试嵌入向量提取压力测试? (可选)${NC}"
                local embed_batch_id="batch_embed_$(date +%s)"
                batch_test_api "$token" "$image_path" "embeddings" "$concurrent" "$total_requests" "$embed_batch_id"
            else
                echo -e "\n${RED}❌ 压力测试失败${NC}"
                exit 1
            fi
        fi
        
        echo -e "\n${GREEN}🎉 批量压力测试完成!${NC}"
        echo -e "${YELLOW}📁 详细测试日志保存在: $BATCH_LOG_DIR/${NC}"
        
    else
        # 原有的标准测试流程
        echo -e "${BLUE}🧪 开始标准测试 Caby AI 人脸检测API${NC}"
        echo -e "${BLUE}===============================================${NC}"
        echo -e "${BLUE}📍 API地址: $BASE_URL${NC}"
        echo -e "${BLUE}📸 测试图像: $image_path${NC}"
        echo -e "${BLUE}🔑 使用token: ${token:0:10}...${token: -10}${NC}"
        echo ""
        
        # 执行测试
        local test_results=()
        
        echo -e "${YELLOW}1. 测试主服务健康检查${NC}"
        if test_main_health; then
            test_results[0]=1
        else
            test_results[0]=0
        fi
        echo ""
        
        echo -e "${YELLOW}2. 测试Vision健康检查${NC}"
        if test_vision_health "$token"; then
            test_results[1]=1
        else
            test_results[1]=0
        fi
        echo ""
        
        echo -e "${YELLOW}3. 测试人脸检测${NC}"
        if test_face_detection "$token" "$image_path"; then
            test_results[2]=1
        else
            test_results[2]=0
        fi
        echo ""
        
        echo -e "${YELLOW}4. 测试嵌入向量提取${NC}"
        if test_embeddings_extraction "$token" "$image_path"; then
            test_results[3]=1
        else
            test_results[3]=0
        fi
        echo ""
        
        # 测试总结
        echo -e "${BLUE}===============================================${NC}"
        echo -e "${BLUE}📋 测试总结:${NC}"
        echo -e "  主服务健康: $([ ${test_results[0]} -eq 1 ] && echo -e "${GREEN}✅ 通过${NC}" || echo -e "${RED}❌ 失败${NC}")"
        echo -e "  Vision健康: $([ ${test_results[1]} -eq 1 ] && echo -e "${GREEN}✅ 通过${NC}" || echo -e "${RED}❌ 失败${NC}")"
        echo -e "  人脸检测: $([ ${test_results[2]} -eq 1 ] && echo -e "${GREEN}✅ 通过${NC}" || echo -e "${RED}❌ 失败${NC}")"
        echo -e "  嵌入向量: $([ ${test_results[3]} -eq 1 ] && echo -e "${GREEN}✅ 通过${NC}" || echo -e "${RED}❌ 失败${NC}")"
        
        local total_passed=$((${test_results[0]} + ${test_results[1]} + ${test_results[2]} + ${test_results[3]}))
        
        if [ $total_passed -eq 4 ]; then
            echo -e "\n${GREEN}🎉 所有测试通过! Caby AI人脸检测API运行正常!${NC}"
            exit 0
        else
            echo -e "\n${YELLOW}⚠️  $total_passed/4 个测试通过，请检查失败项${NC}"
            echo -e "\n${YELLOW}💡 故障排查指南:${NC}"
            echo -e "${YELLOW}  1. 确认服务运行: docker-compose ps${NC}"
            echo -e "${YELLOW}  2. 检查服务日志: docker-compose logs [service_name]${NC}"
            echo -e "${YELLOW}  3. 验证鉴权配置: config/config.yaml -> auth.service_token${NC}"
            echo -e "${YELLOW}  4. 设置环境变量: export CABY_AI_SERVICE_TOKEN=your_token${NC}"
            exit 1
        fi
    fi
}

# 执行主函数
main "$@" 