#!/usr/bin/env python3
"""
Face Detection API Test Script
测试重构后的Face Detection API
"""

import requests
import base64
import json
import os
import yaml
from pathlib import Path
import sys
def get_service_token():
    """获取服务鉴权token"""
    
    # 1. 优先从环境变量获取
    token = os.getenv('CABY_AI_SERVICE_TOKEN')
    if token:
        return token
    
    # 2. 从配置文件获取
    config_paths = [
        "config/config.yaml",
        "../config/config.yaml",
        "/app/config/config.yaml"
    ]
    
    for config_path in config_paths:
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    token = config.get('auth', {}).get('service_token')
                    if token:
                        return token
            except Exception as e:
                print(f"⚠️  读取配置文件 {config_path} 失败: {e}")
                continue
    
    # 3. 使用默认token
    default_token = "03U66tGbSQtHGrh9IyBDjRYaSeQukFga"
    print(f"⚠️  未找到配置的token，使用默认token: {default_token}")
    return default_token

def encode_image_to_base64(image_path: str) -> str:
    """将图像文件编码为base64"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def test_face_detection_api(token: str, image_path: str):
    """测试人脸检测API"""
    
    # API配置
    api_url = "http://localhost:8765/api/v1/vision/detect/face"
    
    if not os.path.exists(image_path):
        print(f"❌ 没有找到测试图像文件: {image_path}")
        return False
    
    print(f"📸 使用测试图像: {image_path}")
    print(f"🔑 使用鉴权token: {token[:10]}...{token[-10:]}")
    
    try:
        # 准备文件上传和鉴权头
        with open(image_path, 'rb') as f:
            files = {'image': f}
            data = {
                'task': 'detect',
                'conf_threshold': '0.2',
                'iou_threshold': '0.5',
                'return_aligned': 'true'
            }
            
            # 添加鉴权头
            headers = {'Authorization': f'Bearer {token}'}
            
            print("🚀 发送人脸检测请求...")
            response = requests.post(api_url, files=files, data=data, headers=headers, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 人脸检测成功!")
            print(f"📊 结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                face_count = result.get('face_count', 0)
                print(f"👥 检测到 {face_count} 个人脸")
                return True
            else:
                print(f"❌ API返回错误: {result.get('error', 'Unknown error')}")
                return False
        elif response.status_code == 401:
            print("❌ 鉴权失败: 请检查service_token是否正确")
            print(f"当前使用的token: {token}")
            print("请检查以下配置:")
            print("  1. 环境变量 CABY_AI_SERVICE_TOKEN")
            print("  2. config/config.yaml 中的 auth.service_token")
            return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 请确保caby_ai服务正在运行 (http://localhost:8765)")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_embeddings_api(token: str):
    """测试嵌入向量API"""
    
    # API配置
    api_url = "http://localhost:8765/api/v1/vision/detect/face"
    
    # 查找测试图像
    test_image_path = None
    possible_paths = [
        "test_images/test_image.jpg",
        "test_images/test.jpg",
        "../test_images/test_image.jpg",
        "../test_images/test.jpg", 
        "/tmp/test.jpg"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            test_image_path = path
            break
    
    if not test_image_path:
        print("❌ 没有找到测试图像文件")
        return False
    
    try:
        # 准备文件上传和鉴权头
        with open(test_image_path, 'rb') as f:
            files = {'image': f}
            data = {
                'task': 'embeddings',
                'conf_threshold': '0.2',
                'iou_threshold': '0.5'
            }
            
            # 添加鉴权头
            headers = {'Authorization': f'Bearer {token}'}
            
            print("🚀 发送嵌入向量提取请求...")
            response = requests.post(api_url, files=files, data=data, headers=headers, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 嵌入向量提取成功!")
            
            if result.get('success'):
                embeddings = result.get('embeddings', [])
                print(f"🔢 提取到 {len(embeddings)} 个人脸的嵌入向量")
                
                for i, emb in enumerate(embeddings):
                    print(f"  人脸 {i+1}: 向量维度 {len(emb.get('embedding', []))}")
                
                return True
            else:
                print(f"❌ API返回错误: {result.get('error', 'Unknown error')}")
                return False
        elif response.status_code == 401:
            print("❌ 鉴权失败: 请检查service_token是否正确")
            return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 嵌入向量测试失败: {str(e)}")
        return False

def test_health_check(token: str):
    """测试健康检查API"""
    
    health_url = "http://localhost:8765/api/v1/vision/health"
    
    try:
        print("🏥 检查Vision健康状态...")
        
        # 添加鉴权头
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(health_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Vision健康检查通过!")
            print(f"📊 状态: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        elif response.status_code == 401:
            print("❌ 健康检查鉴权失败")
            return False
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")
        return False

def test_caby_ai_health():
    """测试Caby AI主服务健康检查（不需要鉴权）"""
    
    health_url = "http://localhost:8765/health"
    
    try:
        print("🏥 检查Caby AI主服务健康状态...")
        response = requests.get(health_url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Caby AI主服务健康检查通过!")
            print(f"📊 状态: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 主服务健康检查失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 主服务健康检查异常: {str(e)}")
        return False

def main():
    if len(sys.argv) < 2:
        print("Usage: python test_face_api.py <image_path>")
        print("Example: python test_face_api.py ../test_images/test_image.jpg")
        return

    image_path = sys.argv[1]
    print(f"🔍 使用测试图像: {image_path}")

    """Usage:
    python test_face_api.py <image_path>
    python test_face_api.py ../test_images/test_image.jpg
    """

    """主测试函数"""
    print("🧪 开始测试重构后的Face Detection API")
    print("=" * 50)
    
    # 获取鉴权token
    service_token = get_service_token()
    print(f"🔑 服务鉴权token: {service_token[:10]}...{service_token[-10:]}")
    
    # 测试主服务健康检查（不需要鉴权）
    print("\n1. 测试Caby AI主服务健康检查")
    main_health_ok = test_caby_ai_health()
    
    # 测试Vision健康检查（需要鉴权）
    print("\n2. 测试Vision健康检查")
    vision_health_ok = test_health_check(service_token)
    
    if not vision_health_ok:
        print("\n❌ Vision健康检查失败，请检查服务是否正常运行和鉴权配置")
        print("💡 排查建议:")
        print("  1. 检查docker-compose服务状态: docker-compose ps")
        print("  2. 检查Vision服务日志: docker-compose logs caby_vision")
        print("  3. 验证service_token配置是否正确")
        return
    
    # 测试人脸检测
    print("\n3. 测试人脸检测")
    detection_ok = test_face_detection_api(service_token, image_path)
    
    # 测试嵌入向量
    print("\n4. 测试嵌入向量提取") 
    embeddings_ok = test_embeddings_api(service_token)
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print(f"  主服务健康: {'✅ 通过' if main_health_ok else '❌ 失败'}")
    print(f"  Vision健康: {'✅ 通过' if vision_health_ok else '❌ 失败'}")
    print(f"  人脸检测: {'✅ 通过' if detection_ok else '❌ 失败'}")
    print(f"  嵌入向量: {'✅ 通过' if embeddings_ok else '❌ 失败'}")
    
    if all([main_health_ok, vision_health_ok, detection_ok, embeddings_ok]):
        print("\n🎉 所有测试通过! Face Detection API重构成功!")
    else:
        print("\n⚠️  部分测试失败，请检查相关配置")
        print("\n💡 故障排查指南:")
        print("  1. 确认服务运行: docker-compose ps")
        print("  2. 检查鉴权配置: config/config.yaml -> auth.service_token")
        print("  3. 设置环境变量: export CABY_AI_SERVICE_TOKEN=your_token")
        print("  4. 查看服务日志: docker-compose logs [service_name]")

if __name__ == "__main__":
    main() 