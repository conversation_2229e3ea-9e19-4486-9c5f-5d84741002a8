#!/usr/bin/env python3
"""
Caby AI & Vision 集成测试脚本
测试caby_ai与caby_vision服务的集成
"""

import requests
import time
import json
import sys
import os

# 服务配置
CABY_AI_URL = "http://localhost:8765"
CABY_VISION_URL = "http://localhost:8001"
SERVICE_TOKEN = os.getenv('CABY_AI_SERVICE_TOKEN', 'default_token')

def check_service_health(service_name, url):
    """检查服务健康状态"""
    print(f"🏥 检查 {service_name} 健康状态...")
    try:
        response = requests.get(f"{url}/health", timeout=10)
        if response.status_code == 200:
            print(f"✅ {service_name} 服务健康")
            return True
        else:
            print(f"❌ {service_name} 服务异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ {service_name} 服务连接失败: {e}")
        return False

def test_caby_ai_vision_health():
    """测试caby_ai的vision健康检查端点"""
    print("\n🔍 测试 Caby AI -> Vision 健康检查...")
    try:
        headers = {'Authorization': f'Bearer {SERVICE_TOKEN}'}
        response = requests.get(f"{CABY_AI_URL}/api/v1/vision/health", headers=headers, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Caby AI -> Vision 健康检查通过")
            print(f"📊 结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            print(f"响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_face_detection_integration():
    """测试人脸检测集成"""
    print("\n📸 测试人脸检测集成...")
    
    # 创建测试图像数据（简单的1x1像素PNG）
    import base64
    # 最小的PNG文件数据
    png_data = base64.b64decode(
        "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
    )
    
    try:
        headers = {'Authorization': f'Bearer {SERVICE_TOKEN}'}
        files = {'image': ('test.png', png_data, 'image/png')}
        data = {
            'task': 'detect',
            'conf_threshold': '0.2',
            'iou_threshold': '0.5',
            'return_aligned': 'true'
        }
        
        response = requests.post(
            f"{CABY_AI_URL}/api/v1/vision/detect/face",
            files=files,
            data=data,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 人脸检测API调用成功")
            print(f"📊 结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 人脸检测失败: HTTP {response.status_code}")
            print(f"响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 人脸检测异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始 Caby AI & Vision 集成测试\n")
    
    # 测试结果追踪
    tests = []
    
    # 1. 检查基础服务健康状态
    print("=" * 50)
    tests.append(("Caby Vision 基础健康", check_service_health("Caby Vision", CABY_VISION_URL)))
    tests.append(("Caby AI 基础健康", check_service_health("Caby AI", CABY_AI_URL)))
    
    # 2. 测试集成功能
    print("=" * 50)
    tests.append(("Vision 健康检查集成", test_caby_ai_vision_health()))
    tests.append(("人脸检测集成", test_face_detection_integration()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    print("=" * 50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有集成测试通过!")
        sys.exit(0)
    else:
        print("⚠️  部分测试失败，请检查服务配置")
        sys.exit(1)

if __name__ == "__main__":
    main() 