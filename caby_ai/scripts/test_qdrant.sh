#!/bin/bash

# Script to test Qdrant functionality

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Load environment variables from .env file if it exists
if [ -f "../.env" ]; then
    source "../.env"
elif [ -f ".env" ]; then
    source ".env"
fi

# Check if Qdrant API key is set
if [ -z "$QDRANT_API_KEY" ]; then
    echo -e "${RED}Error: QDRANT_API_KEY environment variable is not set.${NC}"
    echo -e "Make sure the .env file exists and contains your Qdrant API key."
    exit 1
fi

# Qdrant endpoint
QDRANT_HOST="http://localhost:6333"
COLLECTION_NAME="test_collection"

# Authentication headers
AUTH_HEADER="Api-Key: ${QDRANT_API_KEY}"

echo -e "${GREEN}Starting Qdrant functionality test...${NC}"

# Step 1: Check if Qdrant is healthy
echo -e "${YELLOW}Testing Qdrant health...${NC}"
HEALTH_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" -H "$AUTH_HEADER" ${QDRANT_HOST}/)

if [ "$HEALTH_RESPONSE" == "200" ]; then
    echo -e "${GREEN}Qdrant is healthy!${NC}"
else
    echo -e "${RED}Qdrant health check failed with status code: $HEALTH_RESPONSE${NC}"
    echo "Make sure Qdrant is accessible at ${QDRANT_HOST}"
    echo "Check that your API key is correct"
    exit 1
fi

# Step 2: Check if test collection already exists and delete it if it does
echo -e "${YELLOW}Checking if test collection already exists...${NC}"
CHECK_COLLECTION_RESPONSE=$(curl -s -H "$AUTH_HEADER" ${QDRANT_HOST}/collections/${COLLECTION_NAME})

if echo "$CHECK_COLLECTION_RESPONSE" | grep -q "\"status\":\"green\""; then
    echo -e "${YELLOW}Test collection exists, deleting it...${NC}"
    DELETE_RESPONSE=$(curl -s -X DELETE -H "$AUTH_HEADER" ${QDRANT_HOST}/collections/${COLLECTION_NAME} -o /dev/null -w "%{http_code}")
    
    if [ "$DELETE_RESPONSE" == "200" ]; then
        echo -e "${GREEN}Test collection deleted successfully!${NC}"
    else
        echo -e "${RED}Failed to delete test collection with status code: $DELETE_RESPONSE${NC}"
        exit 1
    fi
fi

# Step 3: Create test collection
echo -e "${YELLOW}Creating test collection...${NC}"
COLLECTION_JSON='{
    "vectors": {
        "size": 384,
        "distance": "Cosine"
    }
}'

CREATE_COLLECTION_RESPONSE_BODY=$(curl -s -X PUT \
    -H "Content-Type: application/json" \
    -H "$AUTH_HEADER" \
    -d "$COLLECTION_JSON" \
    ${QDRANT_HOST}/collections/${COLLECTION_NAME})

CREATE_COLLECTION_STATUS=$(curl -s -X PUT \
    -H "Content-Type: application/json" \
    -H "$AUTH_HEADER" \
    -d "$COLLECTION_JSON" \
    ${QDRANT_HOST}/collections/${COLLECTION_NAME} \
    -o /dev/null -w "%{http_code}")

if [ "$CREATE_COLLECTION_STATUS" == "200" ]; then
    echo -e "${GREEN}Test collection created successfully!${NC}"
else
    echo -e "${RED}Failed to create collection with status code: $CREATE_COLLECTION_STATUS${NC}"
    echo -e "${YELLOW}Response body:${NC}"
    echo "$CREATE_COLLECTION_RESPONSE_BODY" | jq . 2>/dev/null || echo "$CREATE_COLLECTION_RESPONSE_BODY"
    exit 1
fi

# Step 4: Add test data
echo -e "${YELLOW}Adding test data...${NC}"
TEST_VECTOR=$(python3 -c "import json; import random; print(json.dumps([random.random() for _ in range(384)]))")
ADD_DATA_JSON='{
    "points": [
        {
            "id": "test-point-1",
            "vector": '$TEST_VECTOR',
            "payload": {
                "video_id": "test_video_001",
                "animal_id": "cat_001",
                "behavior_type": "normal_poop",
                "is_abnormal": false
            }
        }
    ]
}'

ADD_DATA_RESPONSE_BODY=$(curl -s -X PUT \
    -H "Content-Type: application/json" \
    -H "$AUTH_HEADER" \
    -d "$ADD_DATA_JSON" \
    ${QDRANT_HOST}/collections/${COLLECTION_NAME}/points)

ADD_DATA_STATUS=$(curl -s -X PUT \
    -H "Content-Type: application/json" \
    -H "$AUTH_HEADER" \
    -d "$ADD_DATA_JSON" \
    ${QDRANT_HOST}/collections/${COLLECTION_NAME}/points \
    -o /dev/null -w "%{http_code}")

if [ "$ADD_DATA_STATUS" == "200" ]; then
    echo -e "${GREEN}Test data added successfully!${NC}"
else
    echo -e "${RED}Failed to add test data with status code: $ADD_DATA_STATUS${NC}"
    echo -e "${YELLOW}Response body:${NC}"
    echo "$ADD_DATA_RESPONSE_BODY" | jq . 2>/dev/null || echo "$ADD_DATA_RESPONSE_BODY"
    # Continue with cleanup even if this fails
fi

# Step 5: Query the data
echo -e "${YELLOW}Querying test data...${NC}"
QUERY_RESPONSE=$(curl -s -X GET -H "$AUTH_HEADER" ${QDRANT_HOST}/collections/${COLLECTION_NAME}/points/test-point-1)

if echo "$QUERY_RESPONSE" | grep -q "test_video_001"; then
    echo -e "${GREEN}Query successful! Found test data.${NC}"
else
    echo -e "${RED}Query failed or test data not found.${NC}"
    echo -e "${YELLOW}Response body:${NC}"
    echo "$QUERY_RESPONSE" | jq . 2>/dev/null || echo "$QUERY_RESPONSE"
fi

# Step 6: Perform a search
echo -e "${YELLOW}Testing search functionality...${NC}"
SEARCH_JSON='{
    "vector": '$TEST_VECTOR',
    "limit": 1,
    "with_payload": true
}'

SEARCH_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -H "$AUTH_HEADER" \
    -d "$SEARCH_JSON" \
    ${QDRANT_HOST}/collections/${COLLECTION_NAME}/points/search)

if echo "$SEARCH_RESPONSE" | grep -q "test_video_001"; then
    echo -e "${GREEN}Search successful!${NC}"
else
    echo -e "${RED}Search failed.${NC}"
    echo -e "${YELLOW}Response body:${NC}"
    echo "$SEARCH_RESPONSE" | jq . 2>/dev/null || echo "$SEARCH_RESPONSE"
fi

# Step 7: Clean up - delete test collection
echo -e "${YELLOW}Cleaning up test collection...${NC}"
DELETE_COLLECTION_STATUS=$(curl -s -X DELETE -H "$AUTH_HEADER" ${QDRANT_HOST}/collections/${COLLECTION_NAME} -o /dev/null -w "%{http_code}")

if [ "$DELETE_COLLECTION_STATUS" == "200" ]; then
    echo -e "${GREEN}Test collection deleted successfully!${NC}"
else
    echo -e "${RED}Failed to delete test collection with status code: $DELETE_COLLECTION_STATUS${NC}"
fi

echo -e "${GREEN}Qdrant functionality test completed!${NC}" 