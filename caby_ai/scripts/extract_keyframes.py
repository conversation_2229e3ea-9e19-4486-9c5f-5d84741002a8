#!/usr/bin/env python
import cv2
import argparse
import os
import glob
import numpy as np
import m3u8
import requests
from urllib.parse import urljoin
import json
from datetime import timedelta

# Global variable to track videos with few keyframes but longer duration
fewer_keyframes_videos = []

def extract_keyframes_content_based(video_path, threshold=15.0):
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise IOError("Error opening video stream or file")
    
    keyframes = []
    keyframe_info = []
    prev_frame_gray = None
    frame_count = 0
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # 计算当前帧的时间戳
        frame_count += 1
        timestamp = frame_count / fps
        time_str = str(timedelta(seconds=int(timestamp)))
        
        frame_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        if prev_frame_gray is None:
            keyframes.append(frame)
            keyframe_info.append({
                'frame_number': frame_count,
                'timestamp': timestamp,
                'time_str': time_str,
                'filename': f"keyframe_1.jpg"
            })
            prev_frame_gray = frame_gray
            continue

        diff = cv2.absdiff(frame_gray, prev_frame_gray)
        mean_diff = cv2.mean(diff)[0]

        if mean_diff > threshold:
            keyframes.append(frame)
            keyframe_info.append({
                'frame_number': frame_count,
                'timestamp': timestamp,
                'time_str': time_str,
                'filename': f"keyframe_{len(keyframes)}.jpg"
            })
            prev_frame_gray = frame_gray

    cap.release()
    
    # 保存关键帧和时间戳信息
    video_name = os.path.splitext(os.path.basename(video_path))[0]
    output_dir = os.path.join(os.path.dirname(video_path), video_name)
    os.makedirs(output_dir, exist_ok=True)
    
    # 添加时间间隔信息
    for i in range(len(keyframe_info)-1):
        keyframe_info[i]['time_to_next'] = keyframe_info[i+1]['timestamp'] - keyframe_info[i]['timestamp']
        keyframe_info[i]['time_to_next_str'] = str(timedelta(seconds=int(keyframe_info[i]['time_to_next'])))
    
    # 最后一帧的间隔设为-1表示结束
    if keyframe_info:
        keyframe_info[-1]['time_to_next'] = -1
        keyframe_info[-1]['time_to_next_str'] = "END"
    
    # 保存关键帧图片
    for idx, (frame, info) in enumerate(zip(keyframes, keyframe_info)):
        output_path = os.path.join(output_dir, info['filename'])
        cv2.imwrite(output_path, frame)
    
    # 保存时间戳信息到JSON文件
    timestamp_file = os.path.join(output_dir, 'keyframe_timestamps.json')
    with open(timestamp_file, 'w', encoding='utf-8') as f:
        json.dump({
            'video_name': video_name,
            'total_frames': frame_count,
            'fps': fps,
            'total_duration': frame_count / fps,
            'keyframes': keyframe_info
        }, f, indent=2, ensure_ascii=False)
    
    # Check if this video has few keyframes but long duration
    video_duration = frame_count / fps
    if len(keyframes) < 5 and video_duration >= 10:
        fewer_keyframes_videos.append({
            'path': video_path,
            'keyframe_count': len(keyframes),
            'duration': video_duration
        })
    
    return keyframes, keyframe_info

def process_hls_stream(hls_dir):
    """处理HLS目录，返回排序后的ts文件列表"""
    m3u8_files = glob.glob(os.path.join(hls_dir, '*.m3u8'))
    if not m3u8_files:
        raise ValueError("No m3u8 file found in directory")
    
    # 读取第一个m3u8文件
    playlist = m3u8.load(m3u8_files[0])
    
    # 获取所有ts文件
    ts_files = []
    for segment in playlist.segments:
        ts_path = os.path.join(hls_dir, os.path.basename(segment.uri))
        if os.path.exists(ts_path):
            ts_files.append(ts_path)
    
    return sorted(ts_files)

def extract_keyframes_from_hls(hls_dir, threshold=15.0):
    """从HLS视频流中提取关键帧"""
    ts_files = process_hls_stream(hls_dir)
    keyframes = []
    keyframe_info = []
    prev_frame_gray = None
    total_frames = 0  # 修改：使用总帧数计数
    total_duration = 0
    
    for ts_file in ts_files:
        cap = cv2.VideoCapture(ts_file)
        fps = cap.get(cv2.CAP_PROP_FPS)
        local_frame_count = 0  # 修改：添加局部帧计数
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            local_frame_count += 1
            total_frames += 1  # 修改：增加总帧数
            timestamp = total_duration + local_frame_count / fps
            time_str = str(timedelta(seconds=int(timestamp)))
            
            frame_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            if prev_frame_gray is None:
                keyframes.append(frame)
                keyframe_info.append({
                    'frame_number': total_frames,  # 修改：使用总帧数
                    'timestamp': timestamp,
                    'time_str': time_str,
                    'filename': f"keyframe_{len(keyframes)}.jpg"
                })
                prev_frame_gray = frame_gray
                continue

            diff = cv2.absdiff(frame_gray, prev_frame_gray)
            mean_diff = cv2.mean(diff)[0]

            if mean_diff > threshold:
                keyframes.append(frame)
                keyframe_info.append({
                    'frame_number': total_frames,  # 修改：使用总帧数
                    'timestamp': timestamp,
                    'time_str': time_str,
                    'filename': f"keyframe_{len(keyframes)}.jpg"
                })
                prev_frame_gray = frame_gray
        
        # 修改：更新总时长，但不重置帧计数
        total_duration += local_frame_count / fps
        cap.release()
    
    # 保存关键帧和时间戳信息
    output_dir = os.path.join(hls_dir, 'keyframes')
    os.makedirs(output_dir, exist_ok=True)
    
    # 添加时间间隔信息
    for i in range(len(keyframe_info)-1):
        keyframe_info[i]['time_to_next'] = keyframe_info[i+1]['timestamp'] - keyframe_info[i]['timestamp']
        keyframe_info[i]['time_to_next_str'] = str(timedelta(seconds=int(keyframe_info[i]['time_to_next'])))
    
    if keyframe_info:
        keyframe_info[-1]['time_to_next'] = -1
        keyframe_info[-1]['time_to_next_str'] = "END"
    
    # 保存关键帧图片
    for idx, (frame, info) in enumerate(zip(keyframes, keyframe_info)):
        output_path = os.path.join(output_dir, info['filename'])
        cv2.imwrite(output_path, frame)
    
    # 保存时间戳信息到JSON文件
    timestamp_file = os.path.join(output_dir, 'keyframe_timestamps.json')
    with open(timestamp_file, 'w', encoding='utf-8') as f:
        json.dump({
            'source_directory': hls_dir,
            'total_frames': total_frames,  # 修改：使用正确的总帧数
            'total_duration': total_duration,
            'keyframes': keyframe_info
        }, f, indent=2, ensure_ascii=False)
    
    # Check if this HLS stream has few keyframes but long duration
    if len(keyframes) < 5 and total_duration >= 10:
        fewer_keyframes_videos.append({
            'path': hls_dir,
            'keyframe_count': len(keyframes),
            'duration': total_duration
        })
    
    return keyframes, keyframe_info

def interpolate_frames(frame1, frame2, steps):
    """在两个关键帧之间插入过渡帧"""
    # 计算光流
    frame1_gray = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
    frame2_gray = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)
    flow = cv2.calcOpticalFlowFarneback(frame1_gray, frame2_gray, None, 
                                       pyr_scale=0.5, levels=3, winsize=15, 
                                       iterations=3, poly_n=5, poly_sigma=1.2, 
                                       flags=0)
    
    interpolated_frames = []
    height, width = frame1.shape[:2]
    
    for step in range(1, steps):
        # 计算当前步骤的权重
        weight = step / steps
        
        # 创建网格
        y, x = np.mgrid[0:height, 0:width].astype(np.float32)
        
        # 计算位移
        pos_x = x + flow[..., 0] * weight
        pos_y = y + flow[..., 1] * weight
        
        # 使用重映射进行插值
        interpolated = cv2.remap(frame1, pos_x, pos_y, 
                               cv2.INTER_LINEAR, borderMode=cv2.BORDER_REFLECT)
        
        # 混合两帧
        blend = cv2.addWeighted(interpolated, 1 - weight, 
                              frame2, weight, 0)
        
        interpolated_frames.append(blend)
    
    return interpolated_frames

def reconstruct_video_from_keyframes(keyframes_dir, output_path, fps=30, frames_between=5):
    image_files = sorted(glob.glob(os.path.join(keyframes_dir, 'keyframe_*.jpg')))
    if not image_files:
        raise ValueError("No keyframe images found in the directory")
    
    first_frame = cv2.imread(image_files[0])
    height, width = first_frame.shape[:2]
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # 写入第一帧
    out.write(first_frame)
    
    # 处理每对相邻关键帧
    for i in range(len(image_files) - 1):
        frame1 = cv2.imread(image_files[i])
        frame2 = cv2.imread(image_files[i + 1])
        
        # 生成并写入插值帧
        interpolated = interpolate_frames(frame1, frame2, frames_between + 1)
        for frame in interpolated:
            out.write(frame.astype(np.uint8))
        
        # 写入下一个关键帧
        out.write(frame2)
    
    out.release()
    print(f"视频已重建并保存到: {output_path}")

def process_video_batch(input_dir, threshold=15.0, is_hls=False):
    """批量处理目录下的所有视频文件或HLS目录"""
    processed_count = 0
    if is_hls:
        # 处理所有包含m3u8文件的子目录
        for root, dirs, files in os.walk(input_dir):
            if any(f.endswith('.m3u8') for f in files):
                try:
                    keyframes, info = extract_keyframes_from_hls(root, threshold)
                    print(f"处理HLS目录 {root}: 提取到 {len(keyframes)} 个关键帧")
                    processed_count += 1
                except Exception as e:
                    print(f"处理目录 {root} 时出错: {str(e)}")
    else:
        # 处理所有视频文件
        video_extensions = ('.mp4', '.avi', '.mov', '.mkv')
        for root, _, files in os.walk(input_dir):
            for file in files:
                if file.lower().endswith(video_extensions):
                    video_path = os.path.join(root, file)
                    try:
                        keyframes, info = extract_keyframes_content_based(video_path, threshold)
                        print(f"处理视频 {video_path}: 提取到 {len(keyframes)} 个关键帧")
                        processed_count += 1
                    except Exception as e:
                        print(f"处理视频 {video_path} 时出错: {str(e)}")
    
    return processed_count

def main():
    parser = argparse.ArgumentParser(description='Video keyframe extraction and reconstruction')
    subparsers = parser.add_subparsers(dest='command', help='Commands')
    
    # 提取关键帧的命令
    extract_parser = subparsers.add_parser('extract', help='Extract keyframes from video')
    extract_parser.add_argument('input_path', type=str, help='Path to the input video file, HLS directory, or directory containing multiple videos')
    extract_parser.add_argument('--threshold', type=float, default=15.0, 
                              help='Threshold for keyframe detection (default: 15.0)')
    extract_parser.add_argument('--hls', action='store_true', help='Input is HLS directory')
    extract_parser.add_argument('--batch', action='store_true', help='Process all videos/directories in the input path')
    
    # 更新重建视频的命令
    reconstruct_parser = subparsers.add_parser('reconstruct', help='Reconstruct video from keyframes')
    reconstruct_parser.add_argument('keyframes_dir', type=str, help='Directory containing keyframe images')
    reconstruct_parser.add_argument('output_path', type=str, help='Path for the output video')
    reconstruct_parser.add_argument('--fps', type=int, default=30, help='FPS for output video (default: 30)')
    reconstruct_parser.add_argument('--frames-between', type=int, default=5,
                                  help='Number of frames to interpolate between keyframes (default: 5)')
    
    args = parser.parse_args()
    
    if args.command == 'extract':
        if args.batch:
            processed_count = process_video_batch(args.input_path, args.threshold, args.hls)
            print(f"批量处理完成，共处理 {processed_count} 个视频/目录")
        else:
            if args.hls:
                keyframes, info = extract_keyframes_from_hls(args.input_path, args.threshold)
                print(f"提取到 {len(keyframes)} 个关键帧，并保存到目录: {os.path.join(args.input_path, 'keyframes')}")
            else:
                keyframes, info = extract_keyframes_content_based(args.input_path, args.threshold)
                print(f"提取到 {len(keyframes)} 个关键帧，并保存到目录: {os.path.join(os.path.dirname(args.input_path), os.path.splitext(os.path.basename(args.input_path))[0])}")
        
        # After processing is complete, display videos with few keyframes
        if fewer_keyframes_videos:
            print("\n以下视频关键帧少于5帧但时长大于等于10秒：")
            for video in fewer_keyframes_videos:
                print(f"路径: {video['path']} - 关键帧数: {video['keyframe_count']} - 时长: {video['duration']:.2f}秒")
    elif args.command == 'reconstruct':
        reconstruct_video_from_keyframes(args.keyframes_dir, args.output_path, 
                                       args.fps, args.frames_between)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
