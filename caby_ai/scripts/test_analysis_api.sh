#!/bin/bash

# Script to test the caby_ai analysis API endpoint

# --- Configuration ---
API_BASE_URL="http://***************:8765" # Adjust if your service runs elsewhere
ANALYSIS_ENDPOINT="/api/v1/analyze"
BACKEND_SERVER_URL="http://***************:5678" # URL for backend_server

# Load service token from .env file (expected in parent or current dir)
if [ -f "../.env" ]; then
    source "../.env"
elif [ -f ".env" ]; then
    source ".env"
fi

if [ -z "$CABY_AI_SERVICE_TOKEN" ]; then
    echo "Error: CABY_AI_SERVICE_TOKEN environment variable is not set."
    echo "Make sure the .env file exists and contains the token."
    exit 1
fi
SERVICE_TOKEN="$CABY_AI_SERVICE_TOKEN"

# Sample data (adjust as needed, especially video_path)
# Use realistic IDs if possible, otherwise placeholders
# date: 2025-04-25 05:01:05
VIDEO_ID="93c2531d58f3ce1b67BE7E82"
# Use a device ID known to the backend DB if possible, ensure corresponding user exists
# Check backend_server/database/mysql.sql or backend_server DB directly for valid IDs
DEVICE_ID="202502270220f7cbb4421000" # Example from test_notiflow.sh, ensure this exists in DB
BUCKET_PATH="device$DEVICE_ID" # Example path, adjust as needed
USER_ID="022b605dc3421000"           # Corresponding user ID, ensure this exists in DB
START_TIME_ISO="2025-04-07T23:33:10+08:00" # Example time, adjust as needed

# Convert to Unix timestamp (seconds since epoch)
if command -v date &> /dev/null; then
    if date -j -f "%Y-%m-%dT%H:%M:%S%z" "$START_TIME_ISO" +%s &> /dev/null; then
        # macOS style
        START_TIME_UNIX=$(date -j -f "%Y-%m-%dT%H:%M:%S%z" "$START_TIME_ISO" +%s)
    elif date -d "$START_TIME_ISO" +%s &> /dev/null; then
        # Linux style
        START_TIME_UNIX=$(date -d "$START_TIME_ISO" +%s)
    else
        # Fallback
        echo "Warning: Could not convert time to Unix timestamp. Using current time."
        START_TIME_UNIX=$(date +%s)
    fi
else
    echo "Warning: 'date' command not found. Using placeholder timestamp."
    START_TIME_UNIX=1712505190  # Example timestamp, adjust as needed
fi

# URL-encode the start time for the query parameter
# Use python3 if available for URL encoding
if command -v python3 &> /dev/null; then
    ENCODED_START_TIME=$(python3 -c "import urllib.parse; print(urllib.parse.quote_plus('$START_TIME_ISO'))")
elif command -v python &> /dev/null; then # Fallback to python
    ENCODED_START_TIME=$(python -c "import urllib; print(urllib.quote_plus('$START_TIME_ISO'))")
else
    echo "Warning: python/python3 not found for URL encoding. Using raw start time."
    ENCODED_START_TIME="$START_TIME_ISO"
fi

# Construct the video path URL pointing to the backend_server's HLS playlist endpoint
# NOTE: caby_ai's fetchVideoData currently CANNOT process HLS playlists.
# This test is expected to FAIL with 500 Internal Server Error because fetchVideoData
# will receive M3U8 text instead of raw video data.
# The ultimate fix requires changing fetchVideoData in caby_ai.
VIDEO_PATH="${BACKEND_SERVER_URL}/api/records/videos/get?path=${BUCKET_PATH}&start=${ENCODED_START_TIME}"
# Optional: Add &duration=XXX if needed, but GetPlaylist doesn't strictly require it.

# --- Colors ---
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# --- Helper Function ---
log_step() {
    echo -e "\n${YELLOW}>>> $1${NC}"
}

check_result() {
    local http_code=$1
    local response_body=$2
    local expected_code=$3
    local test_name=$4

    echo "Response Code: $http_code"
    echo "Response Body: $response_body" | jq . 2>/dev/null || echo "Response Body: $response_body"


    if [ "$http_code" -eq "$expected_code" ]; then
        echo -e "${GREEN}✓ Test PASSED: $test_name (Expected: $expected_code, Got: $http_code)${NC}"
        return 0
    else
        echo -e "${RED}✗ Test FAILED: $test_name (Expected: $expected_code, Got: $http_code)${NC}"
        return 1
    fi
}

# --- Pre-checks ---
log_step "Checking prerequisites (curl, jq)"
command -v jq >/dev/null 2>&1 || { echo -e "${RED}Error: jq is not installed. Please install jq.${NC}"; exit 1; }
command -v curl >/dev/null 2>&1 || { echo -e "${RED}Error: curl is not installed. Please install curl.${NC}"; exit 1; }

log_step "Checking caby_ai health endpoint"
HEALTH_CHECK_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE_URL/health")
if [ "$HEALTH_CHECK_CODE" -ne 200 ]; then
    echo -e "${RED}Error: caby_ai service is not reachable or healthy at $API_BASE_URL/health (HTTP $HEALTH_CHECK_CODE)${NC}"
    exit 1
else
    echo -e "${GREEN}✓ caby_ai service is healthy.${NC}"
fi

# --- Test Execution ---
log_step "Preparing analysis request payload"

# Construct JSON payload
# Ensure time format is correct (RFC3339 UTC)
read -r -d '' JSON_PAYLOAD <<EOF
{
  "user_id": "$USER_ID",
  "video_id": "$VIDEO_ID",
  "device_id": "$DEVICE_ID",
  "start_time": "$START_TIME_UNIX",
  "end_time": null,
  "status": 1,
  "process_stage": 0,
  "weight_litter": 4.5,
  "weight_cat": 4.8,
  "weight_waste": 0.3,
  "video_path": "$VIDEO_PATH",
  "file_size": 0,
  "type": "shit",
  "created_at": "$START_TIME_ISO",
  "updated_at": "$START_TIME_ISO"
}
EOF

echo "Payload:"
echo "$JSON_PAYLOAD" | jq .

log_step "Sending request to $API_BASE_URL$ANALYSIS_ENDPOINT"

# Send POST request using curl
# -s: silent
# -w "%{http_code}": output http code on a new line after body
# -X POST: set method to POST
# -H "Content-Type: application/json": set content type header
# -H "Authorization: Bearer $SERVICE_TOKEN": set auth header
# -d "$JSON_PAYLOAD": set request body
RESPONSE=$(curl -s -w "\n%{http_code}" -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $SERVICE_TOKEN" \
    -d "$JSON_PAYLOAD" \
    "$API_BASE_URL$ANALYSIS_ENDPOINT")

# Separate body and status code
HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | sed '$d')

log_step "Checking response"
log_step "(expecting 200 OK with placeholder result - very unlikely)"
check_result "$HTTP_CODE" "$RESPONSE_BODY" 200 "Analysis API Call (expecting placeholder success)"
TEST_EXIT_CODE=$? # Update exit code based on alternative check

echo -e "\n${YELLOW}Test script finished.${NC}"
exit $TEST_EXIT_CODE # Exit with the status of the last check
