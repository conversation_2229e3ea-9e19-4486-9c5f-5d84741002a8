package api

import (
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"caby-ai/config"
	"caby-ai/pkg/recognition"

	"github.com/gin-gonic/gin"
)

// RecognitionHandler 个体识别处理器
type RecognitionHandler struct {
	cfg                   *config.Config
	catRecognitionService *recognition.CatRecognitionService
}

// NewRecognitionHandler 创建新的识别处理器
func NewRecognitionHandler(cfg *config.Config, catRecognitionService *recognition.CatRecognitionService) *RecognitionHandler {
	return &RecognitionHandler{
		cfg:                   cfg,
		catRecognitionService: catRecognitionService,
	}
}

// RecognitionRequest 识别请求
type RecognitionRequest struct {
	UserID    string `json:"user_id" binding:"required"`
	ModelType string `json:"model_type"` // "original" or "infrared", 默认 "infrared"
}

// RecognitionResponse 识别响应
type RecognitionResponse struct {
	Success       bool    `json:"success"`
	IsNewCat      bool    `json:"is_new_cat"`
	CatID         string  `json:"cat_id,omitempty"`
	CatName       string  `json:"cat_name,omitempty"`
	Confidence    float64 `json:"confidence"`
	Similarity    float64 `json:"similarity,omitempty"`
	ModelType     string  `json:"model_type"`
	ShadowMode    bool    `json:"shadow_mode,omitempty"`
	Message       string  `json:"message,omitempty"`
	Error         string  `json:"error,omitempty"`
	RequestID     string  `json:"request_id,omitempty"`
}

// RegisterCatRequest 注册猫咪请求
type RegisterCatRequest struct {
	UserID    string `json:"user_id" binding:"required"`
	CatID     string `json:"cat_id" binding:"required"`
	CatName   string `json:"cat_name" binding:"required"`
	ModelType string `json:"model_type"` // "original" or "infrared", 默认 "infrared"
}

// RegisterCatResponse 注册猫咪响应
type RegisterCatResponse struct {
	Success   bool   `json:"success"`
	CatID     string `json:"cat_id,omitempty"`
	CatName   string `json:"cat_name,omitempty"`
	ModelType string `json:"model_type"`
	Message   string `json:"message,omitempty"`
	Error     string `json:"error,omitempty"`
	RequestID string `json:"request_id,omitempty"`
}

// HandleCatRecognition 处理猫咪个体识别请求
func (rh *RecognitionHandler) HandleCatRecognition(c *gin.Context) {
	requestID := fmt.Sprintf("rec_%d", time.Now().UnixNano())
	
	// 解析表单数据
	var req RecognitionRequest
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, RecognitionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Invalid request format: %v", err),
			RequestID: requestID,
		})
		return
	}

	// 设置默认模型类型
	if req.ModelType == "" {
		req.ModelType = "infrared"
	}

	// 获取上传的图像文件
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		c.JSON(http.StatusBadRequest, RecognitionResponse{
			Success:   false,
			Error:     "No image file provided",
			RequestID: requestID,
		})
		return
	}
	defer file.Close()

	// 检查文件大小
	if header.Size > 10*1024*1024 { // 10MB limit
		c.JSON(http.StatusBadRequest, RecognitionResponse{
			Success:   false,
			Error:     "Image file too large (max 10MB)",
			RequestID: requestID,
		})
		return
	}

	// 读取文件内容
	fileBytes, err := io.ReadAll(file)
	if err != nil {
		c.JSON(http.StatusInternalServerError, RecognitionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to read image file: %v", err),
			RequestID: requestID,
		})
		return
	}

	// 转换为base64
	imageBase64 := base64.StdEncoding.EncodeToString(fileBytes)

	// 创建识别请求
	recognitionReq := &recognition.RecognitionRequest{
		ImageBase64: imageBase64,
		UserID:      req.UserID,
		ModelType:   req.ModelType,
	}

	// 执行识别
	ctx := context.Background()
	result, err := rh.catRecognitionService.RecognizeCat(ctx, recognitionReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, RecognitionResponse{
			Success:   false,
			Error:     fmt.Sprintf("Recognition failed: %v", err),
			RequestID: requestID,
		})
		return
	}

	// 构建响应
	response := RecognitionResponse{
		Success:    true,
		IsNewCat:   result.IsNewCat,
		CatID:      result.CatID,
		CatName:    result.CatName,
		Confidence: result.Confidence,
		Similarity: result.Similarity,
		ModelType:  result.ModelType,
		ShadowMode: result.ShadowMode,
		RequestID:  requestID,
	}

	if result.IsNewCat {
		response.Message = fmt.Sprintf("New cat detected: %s", result.CatName)
	} else {
		response.Message = fmt.Sprintf("Existing cat recognized: %s (similarity: %.4f)", 
			result.CatName, result.Similarity)
	}

	c.JSON(http.StatusOK, response)
}

// HandleRegisterCat 处理猫咪注册请求
func (rh *RecognitionHandler) HandleRegisterCat(c *gin.Context) {
	requestID := fmt.Sprintf("reg_%d", time.Now().UnixNano())
	
	// 解析表单数据
	var req RegisterCatRequest
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, RegisterCatResponse{
			Success:   false,
			Error:     fmt.Sprintf("Invalid request format: %v", err),
			RequestID: requestID,
		})
		return
	}

	// 设置默认模型类型
	if req.ModelType == "" {
		req.ModelType = "infrared"
	}

	// 获取上传的图像文件
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		c.JSON(http.StatusBadRequest, RegisterCatResponse{
			Success:   false,
			Error:     "No image file provided",
			RequestID: requestID,
		})
		return
	}
	defer file.Close()

	// 检查文件大小
	if header.Size > 10*1024*1024 { // 10MB limit
		c.JSON(http.StatusBadRequest, RegisterCatResponse{
			Success:   false,
			Error:     "Image file too large (max 10MB)",
			RequestID: requestID,
		})
		return
	}

	// 读取文件内容
	fileBytes, err := io.ReadAll(file)
	if err != nil {
		c.JSON(http.StatusInternalServerError, RegisterCatResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to read image file: %v", err),
			RequestID: requestID,
		})
		return
	}

	// 转换为base64
	imageBase64 := base64.StdEncoding.EncodeToString(fileBytes)

	// 获取特征
	recognitionReq := &recognition.RecognitionRequest{
		ImageBase64: imageBase64,
		UserID:      req.UserID,
		ModelType:   req.ModelType,
	}

	ctx := context.Background()
	result, err := rh.catRecognitionService.RecognizeCat(ctx, recognitionReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, RegisterCatResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to extract features: %v", err),
			RequestID: requestID,
		})
		return
	}

	// 注册新猫咪
	err = rh.catRecognitionService.RegisterNewCat(
		ctx, req.UserID, req.CatID, req.CatName, result.Features, req.ModelType,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, RegisterCatResponse{
			Success:   false,
			Error:     fmt.Sprintf("Failed to register cat: %v", err),
			RequestID: requestID,
		})
		return
	}

	c.JSON(http.StatusOK, RegisterCatResponse{
		Success:   true,
		CatID:     req.CatID,
		CatName:   req.CatName,
		ModelType: req.ModelType,
		Message:   fmt.Sprintf("Cat registered successfully: %s", req.CatName),
		RequestID: requestID,
	})
}

// HandleGetUserCats 获取用户的所有猫咪
func (rh *RecognitionHandler) HandleGetUserCats(c *gin.Context) {
	requestID := fmt.Sprintf("get_%d", time.Now().UnixNano())
	
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":    false,
			"error":      "user_id is required",
			"request_id": requestID,
		})
		return
	}

	modelType := c.DefaultQuery("model_type", "infrared")

	ctx := context.Background()
	cats, err := rh.catRecognitionService.GetUserCats(ctx, userID, modelType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success":    false,
			"error":      fmt.Sprintf("Failed to get user cats: %v", err),
			"request_id": requestID,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"cats":       cats,
		"count":      len(cats),
		"model_type": modelType,
		"request_id": requestID,
	})
}

// HandleDeleteCat 删除猫咪
func (rh *RecognitionHandler) HandleDeleteCat(c *gin.Context) {
	requestID := fmt.Sprintf("del_%d", time.Now().UnixNano())
	
	userID := c.Query("user_id")
	catID := c.Query("cat_id")
	modelType := c.DefaultQuery("model_type", "infrared")

	if userID == "" || catID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":    false,
			"error":      "user_id and cat_id are required",
			"request_id": requestID,
		})
		return
	}

	ctx := context.Background()
	err := rh.catRecognitionService.DeleteCat(ctx, userID, catID, modelType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success":    false,
			"error":      fmt.Sprintf("Failed to delete cat: %v", err),
			"request_id": requestID,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"message":    fmt.Sprintf("Cat deleted successfully: %s", catID),
		"request_id": requestID,
	})
}
