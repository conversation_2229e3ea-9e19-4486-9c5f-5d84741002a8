package api

import (
	"caby-ai/config"
	"caby-ai/pkg/auth"

	"github.com/gin-gonic/gin"
	// Import other necessary packages like analysis handler, clients, service
)

// Accept AnalysisHandler, VisionHandler and RecognitionHandler in SetupRouter
func SetupRouter(cfg *config.Config, analysisHandler *AnalysisHandler, visionHandler *VisionHandler, recognitionHandler *RecognitionHandler) *gin.Engine {
	r := gin.Default()

	// Health check endpoint
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// API v1 group with service token authentication
	apiV1 := r.Group("/api/v1")
	apiV1.Use(auth.ServiceTokenAuth(cfg)) // Assuming auth middleware is correctly implemented
	{
		// Use the passed analysisHandler
		apiV1.POST("/analyze", analysisHandler.HandleAnalysisRequest) // Use the actual handler method

		// Vision相关端点
		vision := apiV1.Group("/vision")
		{
			vision.POST("/detect/cat", visionHandler.HandleCatDetection)
			vision.GET("/health", visionHandler.HandleHealthCheck)
		}

		// 个体识别相关端点
		recognition := apiV1.Group("/recognition")
		{
			recognition.POST("/identify", recognitionHandler.HandleCatRecognition)
			recognition.POST("/register", recognitionHandler.HandleRegisterCat)
			recognition.GET("/cats", recognitionHandler.HandleGetUserCats)
			recognition.DELETE("/cats", recognitionHandler.HandleDeleteCat)
		}
	}

	return r
}
