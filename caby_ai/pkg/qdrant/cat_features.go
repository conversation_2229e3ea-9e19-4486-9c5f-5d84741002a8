package qdrant

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"caby-ai/config"
)

// CatFeature 猫咪特征结构
type CatFeature struct {
	CatID       string    `json:"cat_id"`
	UserID      string    `json:"user_id"`
	CatName     string    `json:"cat_name"`
	Features    []float32 `json:"features"`
	FeatureDim  int       `json:"feature_dim"`
	ModelType   string    `json:"model_type"` // "original" or "infrared"
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// CatFeaturesClient 猫咪特征数据库客户端
type CatFeaturesClient struct {
	httpClient *http.Client
	cfg        config.QdrantConfig
	baseURL    string
}

// NewCatFeaturesClient 创建新的猫咪特征客户端
func NewCatFeaturesClient(cfg config.QdrantConfig) (*CatFeaturesClient, error) {
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	baseURL := fmt.Sprintf("%s://%s", cfg.Scheme, cfg.Host)

	log.Printf("Initializing Cat Features Qdrant client for %s", baseURL)

	return &CatFeaturesClient{
		httpClient: client,
		cfg:        cfg,
		baseURL:    baseURL,
	}, nil
}

// makeRequest 发送HTTP请求
func (cfc *CatFeaturesClient) makeRequest(method, endpoint string, body interface{}) (*http.Response, error) {
	var reqBody io.Reader
	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, cfc.baseURL+endpoint, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if cfc.cfg.ApiKey != "" {
		req.Header.Set("api-key", cfc.cfg.ApiKey)
	}

	return cfc.httpClient.Do(req)
}

// EnsureCatFeaturesCollection 确保猫咪特征集合存在
func (cfc *CatFeaturesClient) EnsureCatFeaturesCollection(ctx context.Context) error {
	collectionName := "cat_features"

	// 检查集合是否存在
	resp, err := cfc.makeRequest("GET", fmt.Sprintf("/collections/%s", collectionName), nil)
	if err != nil {
		return fmt.Errorf("failed to check collection existence: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 200 {
		log.Printf("Collection '%s' already exists", collectionName)
		return nil
	}

	if resp.StatusCode != 404 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("unexpected response when checking collection: %d - %s", resp.StatusCode, string(body))
	}

	// 创建集合
	collectionConfig := map[string]interface{}{
		"vectors": map[string]interface{}{
			"size":     512, // 红外模型的特征维度
			"distance": "Cosine",
		},
	}

	resp, err = cfc.makeRequest("PUT", fmt.Sprintf("/collections/%s", collectionName), collectionConfig)
	if err != nil {
		return fmt.Errorf("failed to create collection: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 && resp.StatusCode != 201 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to create collection '%s': %d - %s", collectionName, resp.StatusCode, string(body))
	}

	log.Printf("Created Qdrant collection: %s", collectionName)
	return nil
}

// Point 表示Qdrant点
type CatFeaturePoint struct {
	ID      string                 `json:"id"`
	Vector  []float32              `json:"vector"`
	Payload map[string]interface{} `json:"payload"`
}

// UpsertCatFeaturePoints 批量插入点
type UpsertCatFeaturePoints struct {
	Points []CatFeaturePoint `json:"points"`
}

// SaveCatFeature 保存猫咪特征
func (cfc *CatFeaturesClient) SaveCatFeature(ctx context.Context, feature *CatFeature) error {
	collectionName := "cat_features"

	point := CatFeaturePoint{
		ID:     fmt.Sprintf("%s_%s_%s", feature.UserID, feature.CatID, feature.ModelType),
		Vector: feature.Features,
		Payload: map[string]interface{}{
			"cat_id":      feature.CatID,
			"user_id":     feature.UserID,
			"cat_name":    feature.CatName,
			"feature_dim": feature.FeatureDim,
			"model_type":  feature.ModelType,
			"created_at":  feature.CreatedAt,
			"updated_at":  feature.UpdatedAt,
		},
	}

	upsertReq := UpsertCatFeaturePoints{
		Points: []CatFeaturePoint{point},
	}

	resp, err := cfc.makeRequest("PUT", fmt.Sprintf("/collections/%s/points", collectionName), upsertReq)
	if err != nil {
		return fmt.Errorf("failed to upsert cat feature: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 && resp.StatusCode != 201 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to save cat feature: %d - %s", resp.StatusCode, string(body))
	}

	log.Printf("Saved cat feature for cat_id: %s, user_id: %s, model_type: %s", 
		feature.CatID, feature.UserID, feature.ModelType)
	return nil
}

// SearchRequest 搜索请求
type CatFeatureSearchRequest struct {
	Vector      []float32              `json:"vector"`
	Limit       int                    `json:"limit"`
	WithPayload bool                   `json:"with_payload"`
	Filter      map[string]interface{} `json:"filter,omitempty"`
}

// SearchResult 搜索结果
type CatFeatureSearchResult struct {
	ID      string                 `json:"id"`
	Score   float64                `json:"score"`
	Payload map[string]interface{} `json:"payload"`
}

// SearchResponse 搜索响应
type CatFeatureSearchResponse struct {
	Result []CatFeatureSearchResult `json:"result"`
}

// SearchSimilarCats 搜索相似的猫咪特征（限制在用户范围内）
func (cfc *CatFeaturesClient) SearchSimilarCats(ctx context.Context, userID string, features []float32, modelType string, limit int, threshold float64) ([]CatFeatureSearchResult, error) {
	collectionName := "cat_features"

	// 构建过滤器，只搜索该用户的猫咪
	filter := map[string]interface{}{
		"must": []map[string]interface{}{
			{
				"key":   "user_id",
				"match": map[string]interface{}{"value": userID},
			},
			{
				"key":   "model_type",
				"match": map[string]interface{}{"value": modelType},
			},
		},
	}

	searchReq := CatFeatureSearchRequest{
		Vector:      features,
		Limit:       limit,
		WithPayload: true,
		Filter:      filter,
	}

	resp, err := cfc.makeRequest("POST", fmt.Sprintf("/collections/%s/points/search", collectionName), searchReq)
	if err != nil {
		return nil, fmt.Errorf("failed to search similar cats: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("search failed: %d - %s", resp.StatusCode, string(body))
	}

	var searchResp CatFeatureSearchResponse
	if err := json.NewDecoder(resp.Body).Decode(&searchResp); err != nil {
		return nil, fmt.Errorf("failed to decode search response: %w", err)
	}

	// 过滤结果，只返回相似度高于阈值的结果
	var filteredResults []CatFeatureSearchResult
	for _, result := range searchResp.Result {
		if result.Score >= threshold {
			filteredResults = append(filteredResults, result)
		}
	}

	return filteredResults, nil
}

// GetCatFeaturesByUser 获取用户的所有猫咪特征
func (cfc *CatFeaturesClient) GetCatFeaturesByUser(ctx context.Context, userID string, modelType string) ([]CatFeatureSearchResult, error) {
	collectionName := "cat_features"

	// 使用scroll API获取所有匹配的点
	filter := map[string]interface{}{
		"must": []map[string]interface{}{
			{
				"key":   "user_id",
				"match": map[string]interface{}{"value": userID},
			},
			{
				"key":   "model_type",
				"match": map[string]interface{}{"value": modelType},
			},
		},
	}

	scrollReq := map[string]interface{}{
		"filter":       filter,
		"limit":        100,
		"with_payload": true,
		"with_vector":  false,
	}

	resp, err := cfc.makeRequest("POST", fmt.Sprintf("/collections/%s/points/scroll", collectionName), scrollReq)
	if err != nil {
		return nil, fmt.Errorf("failed to scroll cat features: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("scroll failed: %d - %s", resp.StatusCode, string(body))
	}

	var scrollResp struct {
		Result struct {
			Points []CatFeatureSearchResult `json:"points"`
		} `json:"result"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&scrollResp); err != nil {
		return nil, fmt.Errorf("failed to decode scroll response: %w", err)
	}

	return scrollResp.Result.Points, nil
}

// DeleteCatFeature 删除猫咪特征
func (cfc *CatFeaturesClient) DeleteCatFeature(ctx context.Context, userID, catID, modelType string) error {
	collectionName := "cat_features"
	pointID := fmt.Sprintf("%s_%s_%s", userID, catID, modelType)

	deleteReq := map[string]interface{}{
		"points": []string{pointID},
	}

	resp, err := cfc.makeRequest("POST", fmt.Sprintf("/collections/%s/points/delete", collectionName), deleteReq)
	if err != nil {
		return fmt.Errorf("failed to delete cat feature: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("delete failed: %d - %s", resp.StatusCode, string(body))
	}

	log.Printf("Deleted cat feature for cat_id: %s, user_id: %s, model_type: %s", catID, userID, modelType)
	return nil
}
