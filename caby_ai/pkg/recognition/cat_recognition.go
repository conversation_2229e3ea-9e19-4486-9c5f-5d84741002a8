package recognition

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"caby-ai/config"
	"caby-ai/pkg/qdrant"
)

// CatRecognitionService 猫咪个体识别服务
type CatRecognitionService struct {
	cfg               *config.Config
	catFeaturesClient *qdrant.CatFeaturesClient
	httpClient        *http.Client
}

// RecognitionRequest 识别请求
type RecognitionRequest struct {
	ImageBase64 string `json:"image_base64"`
	UserID      string `json:"user_id"`
	ModelType   string `json:"model_type"` // "original" or "infrared"
}

// RecognitionResult 识别结果
type RecognitionResult struct {
	IsNewCat   bool      `json:"is_new_cat"`
	CatID      string    `json:"cat_id,omitempty"`
	CatName    string    `json:"cat_name,omitempty"`
	Confidence float64   `json:"confidence"`
	Similarity float64   `json:"similarity,omitempty"`
	Features   []float32 `json:"features,omitempty"`
	FeatureDim int       `json:"feature_dim,omitempty"`
	ModelType  string    `json:"model_type"`
	ShadowMode bool      `json:"shadow_mode,omitempty"`
}

// VisionResponse Vision API响应
type VisionResponse struct {
	Success bool `json:"success"`
	Results struct {
		PredictedCat string    `json:"predicted_cat"`
		Confidence   float64   `json:"confidence"`
		Features     []float32 `json:"features"`
		FeatureDim   int       `json:"feature_dim"`
		ShadowResult *struct {
			PredictedCat string    `json:"predicted_cat"`
			Confidence   float64   `json:"confidence"`
			Features     []float32 `json:"features"`
			FeatureDim   int       `json:"feature_dim"`
			ShadowMode   bool      `json:"shadow_mode"`
			ModelType    string    `json:"model_type"`
		} `json:"shadow_result,omitempty"`
	} `json:"results"`
	Error string `json:"error,omitempty"`
}

// NewCatRecognitionService 创建新的猫咪识别服务
func NewCatRecognitionService(cfg *config.Config, catFeaturesClient *qdrant.CatFeaturesClient) *CatRecognitionService {
	return &CatRecognitionService{
		cfg:               cfg,
		catFeaturesClient: catFeaturesClient,
		httpClient: &http.Client{
			Timeout: time.Duration(cfg.Vision.Timeout) * time.Second,
		},
	}
}

// callVisionAPI 调用Vision API
func (crs *CatRecognitionService) callVisionAPI(imageBase64 string, task string) (*VisionResponse, error) {
	url := fmt.Sprintf("http://%s:%d/%s", crs.cfg.Vision.Host, crs.cfg.Vision.Port, task)

	payload := map[string]interface{}{
		"image":             imageBase64,
		"return_features":   true,
		"return_confidence": true,
		"task":              task,
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", crs.cfg.Vision.ApiKey))

	resp, err := crs.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to call vision API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("vision API error: %d - %s", resp.StatusCode, string(body))
	}

	var visionResp VisionResponse
	if err := json.NewDecoder(resp.Body).Decode(&visionResp); err != nil {
		return nil, fmt.Errorf("failed to decode vision response: %w", err)
	}

	if !visionResp.Success {
		return nil, fmt.Errorf("vision API failed: %s", visionResp.Error)
	}

	return &visionResp, nil
}

// RecognizeCat 识别猫咪个体（同时调用两个模型进行对比）
func (crs *CatRecognitionService) RecognizeCat(ctx context.Context, req *RecognitionRequest) (*RecognitionResult, error) {
	// 同时调用两个模型进行对比
	var primaryResp, shadowResp *VisionResponse
	var primaryErr, shadowErr error

	// 根据请求的模型类型确定主要模型
	if req.ModelType == "infrared" {
		// 红外模型为主，原始模型为影子
		primaryResp, primaryErr = crs.callVisionAPI(req.ImageBase64, "predict_infrared")
		shadowResp, shadowErr = crs.callVisionAPI(req.ImageBase64, "predict")
	} else {
		// 原始模型为主，红外模型为影子
		primaryResp, primaryErr = crs.callVisionAPI(req.ImageBase64, "predict")
		shadowResp, shadowErr = crs.callVisionAPI(req.ImageBase64, "predict_infrared")
	}

	if primaryErr != nil {
		return nil, fmt.Errorf("failed to get features from primary vision API: %w", primaryErr)
	}

	// 记录影子模式结果（即使失败也不影响主流程）
	if shadowErr != nil {
		log.Printf("Shadow mode API call failed: %v", shadowErr)
	}

	// 提取主要模型的特征和结果
	var features []float32
	var featureDim int
	var confidence float64
	var shadowMode bool

	// 从主要模型获取特征用于相似度对比
	if req.ModelType == "infrared" && primaryResp.Results.ShadowResult != nil {
		// 红外模型为主，使用影子模式结果
		features = primaryResp.Results.ShadowResult.Features
		featureDim = primaryResp.Results.ShadowResult.FeatureDim
		confidence = primaryResp.Results.ShadowResult.Confidence
		shadowMode = primaryResp.Results.ShadowResult.ShadowMode
	} else {
		// 使用主模型结果
		features = primaryResp.Results.Features
		featureDim = primaryResp.Results.FeatureDim
		confidence = primaryResp.Results.Confidence
	}

	if len(features) == 0 {
		return nil, fmt.Errorf("no features extracted from image")
	}

	// 在用户的特征库中搜索相似的猫咪
	threshold := crs.cfg.Recognition.SimilarityThreshold
	similarCats, err := crs.catFeaturesClient.SearchSimilarCats(
		ctx, req.UserID, features, req.ModelType, 5, threshold,
	)
	if err != nil {
		log.Printf("Failed to search similar cats: %v", err)
		// 继续处理，作为新猫咪
	}

	result := &RecognitionResult{
		Features:   features,
		FeatureDim: featureDim,
		Confidence: confidence,
		ModelType:  req.ModelType,
		ShadowMode: shadowMode,
	}

	// 添加影子模式对比信息
	if shadowResp != nil && shadowErr == nil {
		var shadowResult string
		var shadowConf float64

		if req.ModelType == "infrared" {
			// 主模型是红外，影子是原始
			shadowResult = shadowResp.Results.PredictedCat
			shadowConf = shadowResp.Results.Confidence
		} else {
			// 主模型是原始，影子是红外
			if shadowResp.Results.ShadowResult != nil {
				shadowResult = shadowResp.Results.ShadowResult.PredictedCat
				shadowConf = shadowResp.Results.ShadowResult.Confidence
			}
		}

		log.Printf("Shadow mode comparison - Primary: %s (%.4f), Shadow: %s (%.4f)",
			result.CatName, result.Confidence, shadowResult, shadowConf)
	}

	if len(similarCats) > 0 {
		// 找到相似的猫咪
		bestMatch := similarCats[0]
		result.IsNewCat = false
		result.CatID = bestMatch.Payload["cat_id"].(string)
		result.CatName = bestMatch.Payload["cat_name"].(string)
		result.Similarity = bestMatch.Score

		log.Printf("Found existing cat: %s (similarity: %.4f)", result.CatName, result.Similarity)
	} else {
		// 新猫咪
		result.IsNewCat = true
		result.CatName = fmt.Sprintf("NewCat%s", time.Now().Format("20060102150405"))

		log.Printf("New cat detected: %s", result.CatName)
	}

	return result, nil
}

// RegisterNewCat 注册新猫咪
func (crs *CatRecognitionService) RegisterNewCat(ctx context.Context, userID, catID, catName string, features []float32, modelType string) error {
	catFeature := &qdrant.CatFeature{
		CatID:      catID,
		UserID:     userID,
		CatName:    catName,
		Features:   features,
		FeatureDim: len(features),
		ModelType:  modelType,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	err := crs.catFeaturesClient.SaveCatFeature(ctx, catFeature)
	if err != nil {
		return fmt.Errorf("failed to save cat feature: %w", err)
	}

	log.Printf("Registered new cat: %s (cat_id: %s, user_id: %s, model_type: %s)",
		catName, catID, userID, modelType)
	return nil
}

// UpdateCatFeature 更新猫咪特征
func (crs *CatRecognitionService) UpdateCatFeature(ctx context.Context, userID, catID, catName string, features []float32, modelType string) error {
	catFeature := &qdrant.CatFeature{
		CatID:      catID,
		UserID:     userID,
		CatName:    catName,
		Features:   features,
		FeatureDim: len(features),
		ModelType:  modelType,
		CreatedAt:  time.Now(), // 这里应该保留原始创建时间，但为了简化先用当前时间
		UpdatedAt:  time.Now(),
	}

	err := crs.catFeaturesClient.SaveCatFeature(ctx, catFeature)
	if err != nil {
		return fmt.Errorf("failed to update cat feature: %w", err)
	}

	log.Printf("Updated cat feature: %s (cat_id: %s, user_id: %s, model_type: %s)",
		catName, catID, userID, modelType)
	return nil
}

// GetUserCats 获取用户的所有猫咪
func (crs *CatRecognitionService) GetUserCats(ctx context.Context, userID, modelType string) ([]qdrant.CatFeatureSearchResult, error) {
	return crs.catFeaturesClient.GetCatFeaturesByUser(ctx, userID, modelType)
}

// DeleteCat 删除猫咪特征
func (crs *CatRecognitionService) DeleteCat(ctx context.Context, userID, catID, modelType string) error {
	return crs.catFeaturesClient.DeleteCatFeature(ctx, userID, catID, modelType)
}
